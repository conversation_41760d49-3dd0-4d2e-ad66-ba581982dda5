# MorriQuiz Server Starter (PowerShell)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    MorriQuiz Server Starter" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get Tailscale IP
Write-Host "Checking Tailscale status..." -ForegroundColor Yellow
try {
    $tailscaleStatus = tailscale status 2>$null
    $tailscaleIP = ($tailscaleStatus | Select-String "morflix" | ForEach-Object { $_.Line.Split()[0] })
    
    if ($tailscaleIP) {
        Write-Host "Tailscale IP found: $tailscaleIP" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Tailscale IP not found!" -ForegroundColor Red
        Write-Host "Make sure Tailscale is running and connected." -ForegroundColor Red
        $tailscaleIP = "Not Available"
    }
} catch {
    Write-Host "WARNING: Could not check Tailscale status!" -ForegroundColor Red
    $tailscaleIP = "Not Available"
}

# Get local IP
$localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.InterfaceAlias -like "*Wi-Fi*" -or $_.InterfaceAlias -like "*Ethernet*" } | Select-Object -First 1).IPAddress

if (-not $localIP) {
    $localIP = "127.0.0.1"
}

Write-Host "Local IP: $localIP" -ForegroundColor Green
Write-Host ""

Write-Host "Starting MorriQuiz server on port 8080..." -ForegroundColor Yellow
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Access URLs:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Local Access:" -ForegroundColor White
Write-Host "  http://localhost:8080" -ForegroundColor Gray
Write-Host "  http://$localIP:8080" -ForegroundColor Gray
Write-Host ""

Write-Host "Tailscale Access:" -ForegroundColor White
if ($tailscaleIP -ne "Not Available") {
    Write-Host "  http://$tailscaleIP:8080" -ForegroundColor Green
    Write-Host ""
    Write-Host "Share this URL with others on your Tailscale network:" -ForegroundColor Yellow
    Write-Host "  http://$tailscaleIP:8080" -ForegroundColor Cyan
} else {
    Write-Host "  Not Available (Tailscale not connected)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Server is starting..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the MorriQuiz Music Server
try {
    if (Test-Path "music-server.py") {
        Write-Host "Starting MorriQuiz Music Server with directory scanning..." -ForegroundColor Green
        python music-server.py
    } else {
        Write-Host "music-server.py not found, using basic HTTP server..." -ForegroundColor Yellow
        python -m http.server 8080 --bind 0.0.0.0
    }
} catch {
    Write-Host ""
    Write-Host "Error starting server. Make sure Python is installed." -ForegroundColor Red
    Write-Host "You can also try: py music-server.py" -ForegroundColor Yellow
    pause
}
