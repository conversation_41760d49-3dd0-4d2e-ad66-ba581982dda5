#!/usr/bin/env python3
"""
Check which files still need year corrections
"""

import os
from pathlib import Path

def check_files_in_directory(decade_dir):
    """Check which files still have incorrect years."""
    decade = decade_dir.name
    mp3_files = list(decade_dir.glob("*.mp3"))
    
    if not mp3_files:
        return
    
    print(f"\n=== Files in {decade} ===")
    
    files_with_2014 = []
    other_files = []
    
    for mp3_file in mp3_files:
        filename = mp3_file.name
        if " - 2014.mp3" in filename:
            files_with_2014.append(filename)
        else:
            other_files.append(filename)
    
    if files_with_2014:
        print(f"\n🔍 Files still with 2014 ({len(files_with_2014)}):")
        for filename in sorted(files_with_2014):
            print(f"  - {filename}")
    
    if other_files:
        print(f"\n✅ Files with corrected years ({len(other_files)}):")
        for filename in sorted(other_files)[:5]:  # Show first 5
            print(f"  - {filename}")
        if len(other_files) > 5:
            print(f"  ... and {len(other_files) - 5} more")

def main():
    """Main function to check remaining files."""
    music_dir = Path("music")
    
    if not music_dir.exists():
        print("Error: music/ directory not found!")
        return
    
    print("🎵 MorriQuiz Year Check")
    print("=" * 50)
    
    # Process each decade directory
    for decade in ['60s', '70s', '80s', '90s', '00s', '10s', '20s']:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            check_files_in_directory(decade_dir)

if __name__ == "__main__":
    main()
