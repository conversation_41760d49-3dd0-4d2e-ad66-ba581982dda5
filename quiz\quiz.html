<!DOCTYPE html>
<html lang="nl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MorriQuiz - <PERSON><PERSON><PERSON> Quiz</title>
    <link rel="icon" type="image/png" href="../logo.png">
    <link rel="stylesheet" href="../css/quiz.css">
  </head>
  <body>
    <!-- Loader -->
    <div id="loader">
      <div class="loader"></div>
    </div>

    <!-- Quiz Header -->
    <header class="quiz-header">
      <div class="header-content">
        <div class="header-logo">
          <img src="../logo.png" alt="MorriQuiz Logo" class="header-logo-img">
        </div>
        <div class="quiz-info">
          <span id="player-info">Speler: <strong id="current-player">-</strong></span>
          <span id="decade-info">Decade: <strong id="current-decade">-</strong></span>
          <span id="score-info">Score: <strong id="current-score">0</strong></span>
        </div>
      </div>
    </header>

    <!-- Quiz Container -->
    <div class="quiz-container">
      <!-- Audio Player -->
      <div class="audio-section">
        <div class="audio-player">
          <audio id="quiz-audio" controls preload="auto" loop>
            Je browser ondersteunt geen audio element.
          </audio>
          <div class="audio-info">
            <p id="audio-status">Muziek wordt geladen...</p>
            <p style="font-size: 0.9rem; color: #888;">💡 Muziek start automatisch bij elke vraag</p>
          </div>
          <div class="audio-controls">
            <button id="play-btn" onclick="playAudio()" class="audio-btn">▶️ Afspelen</button>
            <button id="pause-btn" onclick="pauseAudio()" class="audio-btn">⏸️ Pauzeren</button>
            <button id="restart-btn" onclick="restartAudio()" class="audio-btn">🔄 Opnieuw</button>
          </div>
        </div>
      </div>

      <!-- Question Section -->
      <div class="question-section">
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
          <span class="progress-text" id="progress-text">Vraag 1 van 100</span>
        </div>
        
        <h2 id="question-text">Vraag wordt geladen...</h2>
        
        <div class="options-container">
          <ul class="options" id="options-list">
            <!-- Options worden hier dynamisch geladen -->
          </ul>
        </div>
      </div>

      <!-- Feedback Section -->
      <div id="feedback-section" class="feedback-section hidden">
        <div id="feedback-message" class="feedback-message"></div>
        <div id="explanation" class="explanation"></div>
        <button id="next-btn" class="next-btn" onclick="loadNextQuestion()">
          Volgende Vraag →
        </button>
      </div>

      <!-- Navigation -->
      <div class="navigation">
        <button id="back-btn" class="nav-btn secondary" onclick="goBack()">
          ← Terug naar Menu
        </button>
        <button id="pause-btn" class="nav-btn secondary" onclick="pauseQuiz()">
          ⏸️ Pauzeren
        </button>
      </div>
    </div>

    <!-- Quiz Complete Modal -->
    <div id="quiz-complete-modal" class="modal hidden">
      <div class="modal-content">
        <h2>🎉 Quiz Voltooid!</h2>
        <div class="final-score">
          <p>Je eindScore:</p>
          <span id="final-score-display">0</span>
          <p>van de 200 punten</p>
        </div>
        <div class="score-breakdown">
          <p><strong id="correct-answers">0</strong> van de <strong>100</strong> vragen goed beantwoord</p>
          <p>Percentage: <strong id="percentage-score">0%</strong></p>
        </div>
        <div class="modal-actions">
          <button onclick="playAgain()" class="primary-btn">Opnieuw Spelen</button>
          <button onclick="goToMenu()" class="secondary-btn">Terug naar Menu</button>
          <button onclick="viewLeaderboard()" class="secondary-btn">Leaderboard</button>
        </div>
      </div>
    </div>

    <!-- Pause Modal -->
    <div id="pause-modal" class="modal hidden">
      <div class="modal-content">
        <h2>⏸️ Quiz Gepauzeerd</h2>
        <p>Je quiz is gepauzeerd. Je voortgang is opgeslagen.</p>
        <div class="modal-actions">
          <button onclick="resumeQuiz()" class="primary-btn">Doorgaan</button>
          <button onclick="goToMenu()" class="secondary-btn">Terug naar Menu</button>
        </div>
      </div>
    </div>

    <footer class="footer">
      <p>
        Ontworpen met ❤️ door
        <a href="#" target="_blank">MorriQuiz Team</a>
      </p>
    </footer>

    <!-- Scripts -->
    <script src="../js/musicLoader.js?v=7"></script>
    <script src="../js/script.js?v=7"></script>
    <script src="../js/quiz.js?v=7"></script>
  </body>
</html>
