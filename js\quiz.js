// MorriQuiz - Quiz Logic JavaScript

class QuizGame {
  constructor() {
    this.musicLoader = new MusicLoader();
    this.currentPlayer = null;
    this.currentDecade = null;
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.score = 0;
    this.correctAnswers = 0;
    this.isQuizActive = false;
    this.currentAudio = null;
    this.quizSession = null;
    this.answers = []; // Track all answers
    this.startTime = null;
    this.endTime = null;

    this.init();
  }

  async init() {
    // Hide loader
    window.addEventListener("load", () => {
      document.getElementById("loader").style.display = "none";
    });

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    this.currentPlayer = urlParams.get('player');
    this.currentDecade = urlParams.get('decade');

    if (!this.currentPlayer || !this.currentDecade) {
      alert('Ongeldige quiz parameters. Je wordt teruggeleid naar het hoofdmenu.');
      window.location.href = '../index.html';
      return;
    }

    // Update UI with player info
    this.updatePlayerInfo();

    try {
      // Load music for the selected decade
      await this.musicLoader.loadDecadeMusic(this.currentDecade);
      
      // Get random songs and generate questions (100 questions)
      const songs = this.musicLoader.getRandomSongs(this.currentDecade, 100);
      this.questions = this.musicLoader.generateQuestions(songs);

      // Validate that we have valid questions
      if (this.questions.length === 0) {
        throw new Error('Geen geldige vragen kunnen worden gegenereerd');
      }

      // Start the quiz
      this.startQuiz();
    } catch (error) {
      console.error('Fout bij laden van quiz:', error);
      alert(`Fout bij laden van muziek voor ${this.currentDecade}: ${error.message}`);
      window.location.href = '../index.html';
    }
  }

  updatePlayerInfo() {
    document.getElementById('current-player').textContent = this.currentPlayer;
    document.getElementById('current-decade').textContent = this.currentDecade;
    document.getElementById('current-score').textContent = this.score;
  }

  startQuiz() {
    this.isQuizActive = true;
    this.startTime = Date.now(); // Record start time for play duration

    // Create quiz session
    this.quizSession = {
      id: Date.now().toString(),
      player: this.currentPlayer,
      decade: this.currentDecade,
      startTime: new Date().toISOString(),
      currentQuestion: 0,
      score: 0,
      correctAnswers: 0,
      answers: [],
      isCompleted: false
    };

    // Try to load existing session
    this.loadQuizSession();

    this.loadQuestion();
  }

  loadQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeQuiz();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];

    // Update progress
    this.updateProgress();

    // Load audio
    this.loadAudio(question.song);

    // Display question
    this.displayQuestion(question);

    // Hide feedback section
    document.getElementById('feedback-section').classList.add('hidden');

    // Auto-save current progress
    this.saveQuizSession();

    // Small delay to ensure audio is loaded before trying to play
    setTimeout(() => {
      this.autoPlayAudio();
    }, 500);
  }

  // Load existing quiz session if available
  loadQuizSession() {
    const sessionKey = `morriQuiz_session_${this.currentPlayer}_${this.currentDecade}`;
    const savedSession = localStorage.getItem(sessionKey);

    if (savedSession) {
      try {
        const session = JSON.parse(savedSession);

        // Check if session is from today (optional - you can remove this check)
        const sessionDate = new Date(session.startTime);
        const today = new Date();
        const isToday = sessionDate.toDateString() === today.toDateString();

        if (!session.isCompleted && isToday) {
          // Restore session
          this.currentQuestionIndex = session.currentQuestion;
          this.score = session.score;
          this.correctAnswers = session.correctAnswers;
          this.answers = session.answers || [];
          this.quizSession = session;

          console.log(`Quiz sessie hersteld: vraag ${this.currentQuestionIndex + 1} van ${this.questions.length}`);

          // Show restoration message
          if (this.currentQuestionIndex > 0) {
            setTimeout(() => {
              alert(`Welkom terug! Je quiz is hersteld vanaf vraag ${this.currentQuestionIndex + 1}.`);
            }, 1000);
          }
        }
      } catch (error) {
        console.error('Fout bij laden van quiz sessie:', error);
      }
    }
  }

  // Save current quiz session
  saveQuizSession() {
    if (!this.quizSession) return;

    this.quizSession.currentQuestion = this.currentQuestionIndex;
    this.quizSession.score = this.score;
    this.quizSession.correctAnswers = this.correctAnswers;
    this.quizSession.answers = this.answers;
    this.quizSession.lastSaved = new Date().toISOString();

    const sessionKey = `morriQuiz_session_${this.currentPlayer}_${this.currentDecade}`;
    localStorage.setItem(sessionKey, JSON.stringify(this.quizSession));

    // Also save to main game data
    if (window.MorriQuiz && window.MorriQuiz.gameData) {
      const player = window.MorriQuiz.gameData.players[this.currentPlayer];
      if (player) {
        if (!player.activeSessions) {
          player.activeSessions = {};
        }
        player.activeSessions[this.currentDecade] = this.quizSession;
        window.MorriQuiz.saveGameData();
      }
    }
  }

  updateProgress() {
    const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
    document.getElementById('progress-fill').style.width = `${progress}%`;
    document.getElementById('progress-text').textContent = 
      `Vraag ${this.currentQuestionIndex + 1} van ${this.questions.length}`;
  }

  loadAudio(song) {
    const audio = document.getElementById('quiz-audio');
    const audioStatus = document.getElementById('audio-status');

    // Reset audio
    audio.pause();
    audio.currentTime = 0;

    // Load the actual MP3 file
    audio.src = song.path;
    audioStatus.textContent = `🎵 Muziek wordt geladen...`;

    // Handle successful loading
    audio.onloadeddata = () => {
      audioStatus.textContent = `🎵 Muziek geladen - Start automatisch`;
      // Automatically start playing
      this.autoPlayAudio();
    };

    audio.oncanplay = () => {
      audioStatus.textContent = `🎵 Muziek speelt nu`;
      // Automatically start playing when ready
      this.autoPlayAudio();
    };

    // Handle loading errors
    audio.onerror = () => {
      audioStatus.textContent = `❌ Kan muziek niet afspelen - Bestand niet gevonden`;
      console.error(`Audio bestand niet gevonden: ${song.path}`);

      // Show user-friendly error without revealing song details
      setTimeout(() => {
        alert('Muziekbestand kan niet worden geladen. Controleer of alle MP3 bestanden correct zijn geplaatst in de music mappen.');
      }, 500);
    };

    // Set volume to reasonable level
    audio.volume = 0.7;
  }

  // Automatically play audio with user interaction handling
  autoPlayAudio() {
    const audio = document.getElementById('quiz-audio');
    const audioStatus = document.getElementById('audio-status');

    // Check if audio source is valid
    if (!audio.src || audio.src === '') {
      audioStatus.textContent = '❌ Geen muziekbestand geladen';
      return;
    }

    // Try to play automatically
    const playPromise = audio.play();

    if (playPromise !== undefined) {
      playPromise.then(() => {
        // Audio started playing successfully
        console.log('Audio started playing automatically');
        audioStatus.textContent = '🎵 Muziek speelt nu';

        // Stop after 30 seconds to give time to answer
        setTimeout(() => {
          if (!audio.paused) {
            audio.pause();
            audioStatus.textContent = '⏸️ Automatisch gepauzeerd na 30 seconden';
          }
        }, 30000);

      }).catch(error => {
        // Auto-play was prevented by browser or file error
        console.log('Auto-play prevented or file error:', error);

        if (error.name === 'NotSupportedError' || error.name === 'NotAllowedError') {
          audioStatus.textContent = '🎵 Klik op ▶️ om muziek te starten';
        } else {
          audioStatus.textContent = '❌ Muziekbestand kan niet worden afgespeeld';
        }
      });
    }
  }

  displayQuestion(question) {
    document.getElementById('question-text').textContent = question.question;
    
    const optionsList = document.getElementById('options-list');
    optionsList.innerHTML = '';
    
    const optionLabels = ['A', 'B', 'C', 'D'];
    
    question.options.forEach((option, index) => {
      const li = document.createElement('li');
      li.innerHTML = `
        <span class="option-label">${optionLabels[index]}</span>
        <span class="option-text">${option}</span>
        <div class="option-icon" id="icon-${index}"></div>
      `;
      
      li.onclick = () => this.selectAnswer(li, option, question, `icon-${index}`);
      optionsList.appendChild(li);
    });
  }

  selectAnswer(selectedOption, chosenAnswer, question, iconId) {
    if (!this.isQuizActive) return;

    // Pause audio when answer is selected
    const audio = document.getElementById('quiz-audio');
    audio.pause();

    // Disable all options
    const options = document.querySelectorAll('.options li');
    options.forEach(opt => opt.onclick = null);
    
    const isCorrect = chosenAnswer === question.correct;
    
    if (isCorrect) {
      selectedOption.classList.add('correct');
      this.setIcon(iconId, 'check');
      this.score += 2; // 2 points per correct answer
      this.correctAnswers++;
    } else {
      selectedOption.classList.add('incorrect');
      this.setIcon(iconId, 'cross');
      
      // Highlight correct answer
      options.forEach((opt, idx) => {
        const optionText = opt.querySelector('.option-text').textContent;
        if (optionText === question.correct) {
          opt.classList.add('correct');
          this.setIcon(`icon-${idx}`, 'check');
        }
      });
    }
    
    // Save answer
    this.answers.push({
      questionIndex: this.currentQuestionIndex,
      question: question.question,
      chosenAnswer: chosenAnswer,
      correctAnswer: question.correct,
      isCorrect: isCorrect,
      song: {
        artist: question.song.artist,
        title: question.song.title,
        year: question.song.year
      },
      timestamp: new Date().toISOString()
    });

    // Update score display
    this.updatePlayerInfo();

    // Auto-save progress
    this.saveQuizSession();

    // Show feedback
    this.showFeedback(isCorrect, question);
  }

  setIcon(iconId, type) {
    const icon = document.getElementById(iconId);
    if (type === 'check') {
      icon.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="width: 100%; height: 100%;">
          <path fill="#28a745" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
        </svg>
      `;
    } else if (type === 'cross') {
      icon.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="width: 100%; height: 100%;">
          <path fill="#dc3545" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c9.4-9.4 24.6-9.4 33.9 0l47 47 47-47c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-47 47 47 47c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0l-47-47-47 47c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l47-47-47-47c-9.4-9.4-9.4-24.6 0-33.9z"/>
        </svg>
      `;
    }
  }

  showFeedback(isCorrect, question) {
    const feedbackSection = document.getElementById('feedback-section');
    const feedbackMessage = document.getElementById('feedback-message');
    const explanation = document.getElementById('explanation');
    
    feedbackMessage.textContent = isCorrect ? '🎉 Correct!' : '❌ Helaas, dat is niet juist.';
    feedbackMessage.className = `feedback-message ${isCorrect ? 'correct' : 'incorrect'}`;
    
    explanation.innerHTML = `<strong>Uitleg:</strong><br>${question.explanation}`;
    
    feedbackSection.classList.remove('hidden');
  }

  loadNextQuestion() {
    this.currentQuestionIndex++;
    this.loadQuestion();
  }

  completeQuiz() {
    this.isQuizActive = false;
    this.endTime = Date.now(); // Record end time

    // Stop audio
    const audio = document.getElementById('quiz-audio');
    audio.pause();

    // Calculate play time in seconds
    const playTimeSeconds = this.startTime ? Math.round((this.endTime - this.startTime) / 1000) : 0;

    // Mark session as completed
    if (this.quizSession) {
      this.quizSession.isCompleted = true;
      this.quizSession.endTime = new Date().toISOString();
      this.quizSession.finalScore = this.score;
      this.quizSession.finalCorrectAnswers = this.correctAnswers;
      this.quizSession.playTimeSeconds = playTimeSeconds;
    }

    // Calculate percentage
    const percentage = Math.round((this.correctAnswers / this.questions.length) * 100);

    // Update final score display
    document.getElementById('final-score-display').textContent = this.score;
    document.getElementById('correct-answers').textContent = this.correctAnswers;
    document.getElementById('percentage-score').textContent = `${percentage}%`;

    // Save final results
    this.saveFinalResults();

    // Save score to main system with play time
    if (window.MorriQuiz) {
      window.MorriQuiz.updatePlayerScore(
        this.score,
        this.currentDecade,
        playTimeSeconds,
        this.correctAnswers,
        this.questions.length
      );
    }

    // Show completion modal
    document.getElementById('quiz-complete-modal').classList.remove('hidden');
  }

  // Save final quiz results
  saveFinalResults() {
    if (!this.quizSession) return;

    // Save completed session
    this.saveQuizSession();

    // Save to completed sessions history
    const historyKey = `morriQuiz_history_${this.currentPlayer}`;
    let history = [];

    try {
      const savedHistory = localStorage.getItem(historyKey);
      if (savedHistory) {
        history = JSON.parse(savedHistory);
      }
    } catch (error) {
      console.error('Fout bij laden van geschiedenis:', error);
    }

    // Add current session to history
    history.push({
      ...this.quizSession,
      answers: this.answers
    });

    // Keep only last 50 completed sessions per player
    if (history.length > 50) {
      history = history.slice(-50);
    }

    localStorage.setItem(historyKey, JSON.stringify(history));

    console.log(`Quiz voltooid! Score: ${this.score}/${this.questions.length * 2} (${this.correctAnswers}/${this.questions.length} correct)`);
  }

  pauseQuiz() {
    this.isQuizActive = false;
    
    // Pause audio
    const audio = document.getElementById('quiz-audio');
    audio.pause();
    
    // Show pause modal
    document.getElementById('pause-modal').classList.remove('hidden');
  }

  resumeQuiz() {
    this.isQuizActive = true;
    document.getElementById('pause-modal').classList.add('hidden');
  }
}

// Global functions for button clicks
function loadNextQuestion() {
  if (window.quizGame) {
    window.quizGame.loadNextQuestion();
  }
}

function goBack() {
  if (confirm('Weet je zeker dat je terug wilt naar het menu? Je voortgang gaat verloren.')) {
    window.location.href = '../index.html';
  }
}

function pauseQuiz() {
  if (window.quizGame) {
    window.quizGame.pauseQuiz();
  }
}

function resumeQuiz() {
  if (window.quizGame) {
    window.quizGame.resumeQuiz();
  }
}

function playAgain() {
  window.location.reload();
}

function goToMenu() {
  window.location.href = '../index.html';
}

function viewLeaderboard() {
  // For now, just go to menu where leaderboard is shown
  window.location.href = '../index.html';
}

// Audio control functions
function playAudio() {
  const audio = document.getElementById('quiz-audio');

  if (!audio.src || audio.src === '') {
    alert('Geen muziekbestand geladen. Probeer de pagina te vernieuwen.');
    return;
  }

  audio.play().catch(error => {
    console.log('Could not play audio:', error);
    document.getElementById('audio-status').textContent = '❌ Kan muziek niet afspelen';
    alert('Muziek kan niet worden afgespeeld. Controleer of het MP3 bestand bestaat en correct is geformatteerd.');
  });
}

function pauseAudio() {
  const audio = document.getElementById('quiz-audio');
  audio.pause();
}

function restartAudio() {
  const audio = document.getElementById('quiz-audio');
  audio.currentTime = 0;
  audio.play().catch(error => {
    console.log('Could not restart audio:', error);
  });
}

// Initialize quiz when page loads
window.addEventListener('DOMContentLoaded', () => {
  window.quizGame = new QuizGame();
});
