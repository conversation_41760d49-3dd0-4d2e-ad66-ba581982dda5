# 🚀 MorriQuiz Quick Start Guide

## ✅ Wat Werkt Nu Al

### 10s Decade (2010-2019):
- **91 MP3 bestanden** succesvol toegevoegd en geformatteerd
- **Quiz is volledig functioneel** voor de 10s
- **Alle bestandsnamen** zijn correct geconverteerd naar: `Artiest - Titel - 2014.mp3`

## 🎯 Test de 10s Quiz Nu

1. **Start de server** (als deze nog niet draait):
   ```bash
   python music-server.py
   ```

2. **Open de website**:
   - <PERSON>aal: `http://localhost:8080`
   - Tailscale: `http://*************:8080`

3. **Test de quiz**:
   - Voer je naam in (bijv. "Morris")
   - Klik op "10s" decade
   - Geniet van 100 vragen over 2010s muziek!

## 📁 Andere Decades Toevoegen

De andere mappen zijn nog leeg:
- `music/60s/` - Leeg
- `music/70s/` - Leeg  
- `music/80s/` - Leeg
- `music/90s/` - Leeg
- `music/00s/` - Leeg
- `music/20s/` - Leeg

### Om andere decades toe te voegen:

1. **Plaats MP3 bestanden** in de juiste map
2. **Gebruik elk formaat** - het script zal ze automatisch converteren
3. **Run het fix script** opnieuw:
   ```bash
   python fix-filenames.py
   ```

## 🎵 Voorbeeld Bestanden voor Andere Decades

### Voor 80s map:
```
Michael Jackson - Billie Jean.mp3
Madonna - Like a Virgin.mp3
Prince - Purple Rain.mp3
Queen - Another One Bites the Dust.mp3
```

### Voor 90s map:
```
Nirvana - Smells Like Teen Spirit.mp3
Alanis Morissette - You Oughta Know.mp3
Oasis - Wonderwall.mp3
Radiohead - Creep.mp3
```

### Voor 00s map:
```
Eminem - Lose Yourself.mp3
Beyonce - Crazy in Love.mp3
Coldplay - Yellow.mp3
OutKast - Hey Ya.mp3
```

## 🔧 Filename Fix Script Features

Het `fix-filenames.py` script:
- **Detecteert automatisch** artiest en titel
- **Verwijdert** "(feat.)", "Radio Edit", "Remix" etc.
- **Voegt automatisch jaar toe** gebaseerd op decade
- **Voorkomt duplicaten** door nummering toe te voegen
- **Behoudt originele bestanden** (hernoemt, verwijdert niet)

## 📊 Huidige Status

```
✅ 10s: 91 bestanden - KLAAR VOOR QUIZ
❌ 60s: 0 bestanden - Voeg MP3s toe
❌ 70s: 0 bestanden - Voeg MP3s toe  
❌ 80s: 0 bestanden - Voeg MP3s toe
❌ 90s: 0 bestanden - Voeg MP3s toe
❌ 00s: 0 bestanden - Voeg MP3s toe
❌ 20s: 0 bestanden - Voeg MP3s toe
```

## 🎮 Quiz Features (Nu Beschikbaar)

- **100 vragen** per quiz sessie
- **Automatische opslag** na elke vraag
- **Pauzeren en hervatten** mogelijk
- **Audio afspelen** van MP3 bestanden
- **Random vragen** over artiest, titel, jaar
- **Score tracking** en leaderboard
- **Tailscale toegang** voor externe spelers

## 🚨 Troubleshooting

### Quiz laadt niet voor andere decades:
- **Normaal!** Andere mappen zijn nog leeg
- Voeg MP3 bestanden toe en run `fix-filenames.py`

### 10s quiz werkt niet:
- Controleer of server draait: `python music-server.py`
- Test API: `http://localhost:8080/api/music/10s`
- Controleer browser console voor fouten

### Bestandsnamen verkeerd:
- Run opnieuw: `python fix-filenames.py`
- Script is veilig en kan meerdere keren worden uitgevoerd

## 🎉 Volgende Stappen

1. **Test de 10s quiz** om te zien hoe alles werkt
2. **Voeg bestanden toe** aan andere decades
3. **Run fix script** voor elke nieuwe batch bestanden
4. **Deel de Tailscale URL** met vrienden: `http://*************:8080`

---

**Je hebt nu een volledig werkende muziek quiz met 91 nummers uit de jaren 2010! 🎵🎉**
