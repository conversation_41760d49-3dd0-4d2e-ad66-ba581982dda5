<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MorriQuiz Debug - Account System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 MorriQuiz Debug Panel</h1>
    
    <div class="debug-panel">
        <h2>Script Loading Status</h2>
        <div id="script-status"></div>
    </div>
    
    <div class="debug-panel">
        <h2>Account Manager Status</h2>
        <div id="account-status"></div>
        <button onclick="testAccountManager()">Test Account Manager</button>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testRegister()">Test Register</button>
    </div>
    
    <div class="debug-panel">
        <h2>Network Test</h2>
        <div id="network-status"></div>
        <button onclick="testNetwork()">Test Network Connection</button>
    </div>
    
    <div class="debug-panel">
        <h2>Console Log</h2>
        <div id="console-log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="debug-panel">
        <h2>Quick Actions</h2>
        <button onclick="window.location.href='index.html?v=14'">Go to Main Site</button>
        <button onclick="clearAllData()">Clear All Data</button>
        <button onclick="location.reload()">Reload Page</button>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const logs = [];
        
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('warn', args.join(' '));
        };
        
        function updateLogDisplay() {
            const logDiv = document.getElementById('console-log');
            logDiv.innerHTML = logs.slice(-50).join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            logs.length = 0;
            updateLogDisplay();
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkScriptLoading() {
            console.log('🔍 Checking script loading status...');
            
            let status = '';
            
            // Check if scripts are loaded
            const scripts = [
                { name: 'accounts.js', check: () => window.AccountManager },
                { name: 'script.js', check: () => window.MorriQuiz },
                { name: 'multiplayer.js', check: () => window.MultiplayerManager }
            ];
            
            scripts.forEach(script => {
                const loaded = script.check();
                const statusType = loaded ? 'success' : 'error';
                const statusText = loaded ? '✅ Loaded' : '❌ Not Loaded';
                status += `<div class="status ${statusType}">${script.name}: ${statusText}</div>`;
            });
            
            updateStatus('script-status', status);
        }
        
        function testAccountManager() {
            console.log('🧪 Testing AccountManager...');
            
            if (!window.AccountManager) {
                updateStatus('account-status', '❌ AccountManager not available', 'error');
                return;
            }
            
            try {
                const accounts = window.AccountManager.loadAccounts();
                const accountCount = Object.keys(accounts).length;
                updateStatus('account-status', `✅ AccountManager working. ${accountCount} accounts found.`, 'success');
            } catch (error) {
                updateStatus('account-status', `❌ AccountManager error: ${error.message}`, 'error');
            }
        }
        
        function testLogin() {
            console.log('🧪 Testing login function...');
            
            if (!window.AccountManager) {
                alert('AccountManager not available');
                return;
            }
            
            const username = prompt('Enter test username:');
            const password = prompt('Enter test password:');
            
            if (username && password) {
                try {
                    const user = window.AccountManager.login(username, password);
                    alert(`Login successful! Welcome ${user.username}`);
                } catch (error) {
                    alert(`Login failed: ${error.message}`);
                }
            }
        }
        
        function testRegister() {
            console.log('🧪 Testing register function...');
            
            if (!window.AccountManager) {
                alert('AccountManager not available');
                return;
            }
            
            const username = prompt('Enter new username:');
            const password = prompt('Enter new password:');
            
            if (username && password) {
                try {
                    const user = window.AccountManager.createAccount(username, password);
                    alert(`Registration successful! Welcome ${user.username}`);
                } catch (error) {
                    alert(`Registration failed: ${error.message}`);
                }
            }
        }
        
        function testNetwork() {
            console.log('🌐 Testing network connection...');
            
            fetch(window.location.origin + '/index.html')
                .then(response => {
                    if (response.ok) {
                        updateStatus('network-status', '✅ Network connection OK', 'success');
                    } else {
                        updateStatus('network-status', `⚠️ Network response: ${response.status}`, 'warning');
                    }
                })
                .catch(error => {
                    updateStatus('network-status', `❌ Network error: ${error.message}`, 'error');
                });
        }
        
        function clearAllData() {
            if (confirm('Clear all localStorage data? This will delete all accounts and scores!')) {
                localStorage.clear();
                alert('All data cleared!');
                location.reload();
            }
        }
        
        // Initialize debug panel
        window.addEventListener('load', function() {
            console.log('🔧 Debug panel loaded');
            checkScriptLoading();
            testAccountManager();
            testNetwork();
            
            // Check again after a delay
            setTimeout(() => {
                console.log('🔄 Rechecking after delay...');
                checkScriptLoading();
                testAccountManager();
            }, 2000);
        });
        
        // Listen for AccountManager ready event
        window.addEventListener('accountManagerReady', function() {
            console.log('📢 AccountManager ready event received in debug panel');
            testAccountManager();
        });
    </script>
    
    <!-- Load the same scripts as main site -->
    <script src="js/accounts.js?v=14"></script>
    <script src="js/script.js?v=14"></script>
    <script src="js/multiplayer.js?v=14"></script>
</body>
</html>
