#!/usr/bin/env python3
"""
MorriQuiz MP3 Length Converter
Converts MP3 files to optimal length for quiz gameplay (30-45 seconds)
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_ffmpeg():
    """Check if FFmpeg is available."""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def get_audio_duration(file_path):
    """Get duration of audio file in seconds."""
    try:
        result = subprocess.run([
            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
            '-of', 'csv=p=0', str(file_path)
        ], capture_output=True, text=True, check=True)
        return float(result.stdout.strip())
    except (subprocess.CalledProcessError, ValueError):
        return None

def convert_mp3(input_file, output_file, start_time=20, duration=35):
    """Convert MP3 file to specified length."""
    try:
        subprocess.run([
            'ffmpeg', '-i', str(input_file),
            '-ss', f'00:00:{start_time:02d}',
            '-t', f'00:00:{duration:02d}',
            '-acodec', 'copy',
            str(output_file), '-y'
        ], capture_output=True, check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def process_decade(decade_dir):
    """Process all MP3 files in a decade directory."""
    decade = decade_dir.name
    print(f"  📂 Processing decade: {decade}")
    
    mp3_files = list(decade_dir.glob("*.mp3"))
    if not mp3_files:
        print(f"     ⚠️  No MP3 files found in {decade}")
        return 0, 0, 0
    
    processed = 0
    converted = 0
    errors = 0
    
    for mp3_file in mp3_files:
        processed += 1
        
        # Check if already processed
        backup_file = mp3_file.with_suffix('.mp3.original')
        if backup_file.exists():
            print(f"     ⏭️  Skipping: {mp3_file.name} (already processed)")
            continue
        
        print(f"     🔄 Converting: {mp3_file.name}")
        
        # Get original duration
        duration = get_audio_duration(mp3_file)
        if duration is None:
            print(f"     ❌ Could not read duration: {mp3_file.name}")
            errors += 1
            continue
        
        # Skip if already short enough
        if duration <= 45:
            print(f"     ⏭️  Skipping: {mp3_file.name} (already {duration:.1f}s)")
            continue
        
        # Create backup
        try:
            shutil.copy2(mp3_file, backup_file)
        except Exception as e:
            print(f"     ❌ Backup failed: {mp3_file.name} - {e}")
            errors += 1
            continue
        
        # Calculate optimal start time (25% into song, but not less than 10s)
        start_time = max(10, int(duration * 0.25))
        target_duration = 35
        
        # Convert file
        temp_file = mp3_file.with_suffix('.mp3.temp')
        if convert_mp3(backup_file, temp_file, start_time, target_duration):
            try:
                # Replace original with converted
                shutil.move(temp_file, mp3_file)
                print(f"     ✅ Converted: {mp3_file.name} ({duration:.1f}s → {target_duration}s)")
                converted += 1
            except Exception as e:
                print(f"     ❌ File replacement failed: {mp3_file.name} - {e}")
                # Restore backup
                shutil.move(backup_file, mp3_file)
                errors += 1
        else:
            print(f"     ❌ Conversion failed: {mp3_file.name}")
            # Remove backup since conversion failed
            backup_file.unlink()
            errors += 1
        
        # Clean up temp file if it exists
        if temp_file.exists():
            temp_file.unlink()
    
    print(f"     📊 {decade} Results: {processed} processed, {converted} converted, {errors} errors")
    return processed, converted, errors

def main():
    """Main conversion function."""
    print()
    print("  🎵 MorriQuiz MP3 Length Converter v1.0")
    print("  ═══════════════════════════════════════════════════════════════════════")
    print()
    print("  This tool converts MP3 files to optimal length for quiz gameplay:")
    print("  ├── Trims songs to 30-45 seconds (quiz-friendly length)")
    print("  ├── Keeps the most recognizable part (usually chorus)")
    print("  ├── Maintains original audio quality")
    print("  ├── Creates backup of original files")
    print("  └── Processes all decades automatically")
    print()
    
    # Check FFmpeg
    if not check_ffmpeg():
        print("  ❌ ERROR: FFmpeg not found!")
        print()
        print("  FFmpeg is required for audio processing.")
        print("  Please install FFmpeg:")
        print("  - Windows: Download from https://ffmpeg.org/download.html")
        print("  - macOS: brew install ffmpeg")
        print("  - Linux: sudo apt install ffmpeg (Ubuntu/Debian)")
        print()
        return 1
    
    print("  📊 System Check: FFmpeg ✅")
    print()
    
    # Check music directory
    music_dir = Path("music")
    if not music_dir.exists():
        print("  ❌ ERROR: Music directory not found!")
        print("  Please make sure you're running this from the MorriQuiz root directory.")
        print()
        return 1
    
    print("  📁 Music Directory: Found ✅")
    print()
    
    print("  🎯 Conversion Settings:")
    print("  ├── Target Length: 35 seconds")
    print("  ├── Start Position: 25% into song (to catch chorus)")
    print("  ├── Audio Quality: Original bitrate maintained")
    print("  ├── Format: MP3 (unchanged)")
    print("  └── Backup: Original files saved as .original")
    print()
    
    print("  ⚠️  WARNING: This will modify your MP3 files!")
    print("  Original files will be backed up with .original extension.")
    print()
    
    confirm = input("  Do you want to continue? (Y/N): ").strip().lower()
    if confirm != 'y':
        print("  Operation cancelled.")
        return 0
    
    print()
    print("  🚀 Starting MP3 conversion process...")
    print("  ═══════════════════════════════════════════════════════════════════════")
    print()
    
    total_processed = 0
    total_converted = 0
    total_errors = 0
    
    # Process each decade
    decades = ['60s', '70s', '80s', '90s', '00s', '10s', '20s']
    for decade in decades:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            processed, converted, errors = process_decade(decade_dir)
            total_processed += processed
            total_converted += converted
            total_errors += errors
            print()
        else:
            print(f"  ⚠️  Skipping {decade}: Directory not found")
    
    print("  ═══════════════════════════════════════════════════════════════════════")
    print("  🎉 Conversion Complete!")
    print()
    print("  📊 Summary:")
    print(f"  ├── Total files processed: {total_processed}")
    print(f"  ├── Successfully converted: {total_converted}")
    print(f"  ├── Errors encountered: {total_errors}")
    print(f"  └── Backup files created: {total_converted}")
    print()
    print("  💡 Next Steps:")
    print("  ├── Test the quiz to ensure audio quality")
    print("  ├── Original files are saved as .original (can be restored)")
    print("  ├── Delete .original files once satisfied with results")
    print("  └── Run start-server.bat to test the quiz")
    print()
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n  ❌ Unexpected error: {e}")
        sys.exit(1)
