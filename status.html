<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MorriQuiz Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Status Check</h1>
        
        <div id="status-output">
            <div class="status info">Checking system status...</div>
        </div>
        
        <h3>Actions:</h3>
        <button onclick="checkAPI()">Test API</button>
        <button onclick="checkJavaScript()">Test JavaScript</button>
        <button onclick="checkMusicFiles()">Check Music Files</button>
        <button onclick="window.location.href='index.html'">Go to Quiz</button>
        
        <h3>Debug Info:</h3>
        <pre id="debug-info">Loading...</pre>
    </div>

    <script>
        const output = document.getElementById('status-output');
        const debugInfo = document.getElementById('debug-info');
        
        function addStatus(message, type = 'info') {
            output.innerHTML += `<div class="status ${type}">${message}</div>`;
        }
        
        function updateDebug(info) {
            debugInfo.textContent = info;
        }
        
        // Initial checks
        window.addEventListener('load', function() {
            checkJavaScript();
            checkAPI();
            
            updateDebug(`
Browser: ${navigator.userAgent}
URL: ${window.location.href}
Time: ${new Date().toISOString()}
            `);
        });
        
        async function checkAPI() {
            addStatus('🔍 Testing API endpoints...', 'info');
            
            try {
                const response = await fetch('/api/music/10s');
                if (response.ok) {
                    const data = await response.json();
                    addStatus(`✅ API works! Found ${data.count} files for 10s`, 'success');
                    
                    if (data.files && data.files.length > 0) {
                        addStatus(`📁 First file: ${data.files[0]}`, 'info');
                    }
                } else {
                    addStatus(`❌ API error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addStatus(`❌ API connection failed: ${error.message}`, 'error');
            }
        }
        
        function checkJavaScript() {
            addStatus('🔍 Testing JavaScript loading...', 'info');
            
            // Test if we can load musicLoader
            const script = document.createElement('script');
            script.src = 'js/musicLoader.js?v=' + Date.now();
            script.onload = function() {
                addStatus('✅ musicLoader.js loaded successfully', 'success');
                
                if (typeof MusicLoader !== 'undefined') {
                    addStatus('✅ MusicLoader class is available', 'success');
                    
                    try {
                        const loader = new MusicLoader();
                        addStatus('✅ MusicLoader instance created', 'success');
                    } catch (error) {
                        addStatus(`❌ Error creating MusicLoader: ${error.message}`, 'error');
                    }
                } else {
                    addStatus('❌ MusicLoader class not found', 'error');
                }
            };
            script.onerror = function() {
                addStatus('❌ Failed to load musicLoader.js', 'error');
            };
            document.head.appendChild(script);
        }
        
        async function checkMusicFiles() {
            addStatus('🔍 Checking music files...', 'info');
            
            const decades = ['60s', '70s', '80s', '90s', '00s', '10s', '20s'];
            
            for (const decade of decades) {
                try {
                    const response = await fetch(`/api/music/${decade}`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.count > 0) {
                            addStatus(`✅ ${decade}: ${data.count} files`, 'success');
                        } else {
                            addStatus(`⚠️ ${decade}: No files found`, 'info');
                        }
                    } else {
                        addStatus(`❌ ${decade}: API error`, 'error');
                    }
                } catch (error) {
                    addStatus(`❌ ${decade}: ${error.message}`, 'error');
                }
            }
        }
    </script>
</body>
</html>
