<!DOCTYPE html>
<html lang="nl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MorriQuiz - <PERSON><PERSON><PERSON> Quiz</title>
    <link rel="icon" type="image/png" href="logo.png">
    <link rel="stylesheet" href="css/index.css">
  </head>
  <body>
    <!-- Loader -->
    <div id="loader">
      <div class="loader"></div>
    </div>
    
    <!-- Naam invoer scherm -->
    <div id="name-screen" class="container">
      <div class="logo-container">
        <img src="logo.png" alt="MorriQuiz Logo" class="logo">
      </div>
      <h2>Welkom bij de Muziek Quiz!</h2>
      
      <!-- Login Form -->
      <div id="login-form" class="auth-form">
        <h3>Inloggen</h3>
        <div class="input-container">
          <input type="text" id="login-username" placeholder="Gebruikersnaam" maxlength="20">
          <input type="password" id="login-password" placeholder="Wachtwoord" maxlength="50">
          <button onclick="loginUser()">Inloggen</button>
        </div>
        <p class="auth-switch">Nog geen account? <a href="#" onclick="showRegisterForm()">Registreer hier</a></p>
      </div>

      <!-- Register Form -->
      <div id="register-form" class="auth-form hidden">
        <h3>Account Aanmaken</h3>
        <div class="input-container">
          <input type="text" id="register-username" placeholder="Gebruikersnaam (3-20 karakters)" maxlength="20">
          <input type="password" id="register-password" placeholder="Wachtwoord (min. 4 karakters)" maxlength="50">
          <input type="password" id="register-confirm" placeholder="Bevestig wachtwoord" maxlength="50">
          <button onclick="registerUser()">Account Aanmaken</button>
        </div>
        <p class="auth-switch">Al een account? <a href="#" onclick="showLoginForm()">Log hier in</a></p>
      </div>

      <!-- User Dashboard (shown when logged in) -->
      <div id="user-dashboard" class="user-dashboard hidden">
        <div class="user-welcome">
          <h3>Welkom terug, <span id="current-username"></span>!</h3>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-label">Level:</span>
              <span id="user-level" class="stat-value">1</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Totale Score:</span>
              <span id="user-total-score" class="stat-value">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Games:</span>
              <span id="user-games-played" class="stat-value">0</span>
            </div>
          </div>
          <div class="level-progress">
            <div class="progress-bar">
              <div id="xp-progress" class="progress-fill"></div>
            </div>
            <span id="xp-text" class="xp-text">0 / 1000 XP</span>
          </div>
        </div>
        <button onclick="startQuiz()" class="start-quiz-btn">Start Quiz</button>
        <button onclick="logoutUser()" class="logout-btn">Uitloggen</button>
      </div>

      <div id="auth-error" class="error-message hidden"></div>

      <!-- Loading indicator for account system -->
      <div id="account-loading" class="loading-indicator hidden">
        <div class="loading-spinner"></div>
        <p>Account systeem wordt geladen...</p>
      </div>
      
      <div class="info-section">
        <h3>Waarom een account aanmaken?</h3>
        <div class="benefits-grid">
          <div class="benefit-item">
            <span class="benefit-icon">🏆</span>
            <div class="benefit-text">
              <strong>Punten Opslaan</strong>
              <p>Je scores blijven permanent bewaard</p>
            </div>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">📊</span>
            <div class="benefit-text">
              <strong>Statistieken</strong>
              <p>Bekijk je voortgang per decade</p>
            </div>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">🎖️</span>
            <div class="benefit-text">
              <strong>Achievements</strong>
              <p>Verdien badges en levels</p>
            </div>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">🥇</span>
            <div class="benefit-text">
              <strong>Leaderboard</strong>
              <p>Vergelijk je scores met anderen</p>
            </div>
          </div>
        </div>

        <div class="rules">
          <h3>Spelregels:</h3>
          <ul>
            <li>Kies uit <strong>7 verschillende decennia</strong> (60s - 20s)</li>
            <li>Elke quiz heeft <strong>100 vragen</strong></li>
            <li>Vragen gaan over <strong>artiest, titel of jaar</strong></li>
            <li>Je voortgang wordt <strong>automatisch opgeslagen</strong></li>
          </ul>
        </div>
      </div>
      
      <!-- Leaderboard sectie -->
      <div class="leaderboard-section">
        <h3>🏆 Leaderboard</h3>
        <div id="leaderboard-list">
          <!-- Leaderboard wordt hier geladen -->
        </div>
      </div>
      
      <div class="conducted-by">
        <p>Gemaakt door: MorriQuiz Team</p>
      </div>
    </div>
    
    <!-- Game Mode Selection Screen -->
    <div id="mode-screen" class="container hidden">
      <div class="logo-container">
        <img src="logo.png" alt="MorriQuiz Logo" class="logo">
      </div>
      <h2 id="welcome-message">Welkom, [Naam]!</h2>
      <h3>Kies een spelmodus:</h3>

      <div class="single-mode-selection">
        <button class="mode-btn single-player" onclick="startSingleplayer()">
          <span class="mode-icon">🎵</span>
          <span class="mode-title">Start Quiz</span>
          <span class="mode-desc">Test je muziekkennis!</span>
        </button>
      </div>

      <button onclick="backToAuthScreen()">← Terug</button>
    </div>

    <!-- Decade Selection Screen -->
    <div id="decade-screen" class="container hidden">
      <div class="logo-container">
        <img src="logo.png" alt="MorriQuiz Logo" class="logo">
      </div>
      <h2 id="decade-welcome-message">Kies een decade!</h2>

      <div class="decade-grid">
        <button class="decade-btn" onclick="selectDecade('60s')">
          <span class="decade-year">60s</span>
          <span class="decade-desc">Rock & Roll</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('70s')">
          <span class="decade-year">70s</span>
          <span class="decade-desc">Disco & Funk</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('80s')">
          <span class="decade-year">80s</span>
          <span class="decade-desc">New Wave & Pop</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('90s')">
          <span class="decade-year">90s</span>
          <span class="decade-desc">Grunge & Hip-Hop</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('00s')">
          <span class="decade-year">00s</span>
          <span class="decade-desc">Pop & R&B</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('10s')">
          <span class="decade-year">10s</span>
          <span class="decade-desc">EDM & Indie</span>
        </button>
        <button class="decade-btn" onclick="selectDecade('20s')">
          <span class="decade-year">20s</span>
          <span class="decade-desc">Streaming Era</span>
        </button>
      </div>
      
      <button id="back-to-mode" onclick="backToModeSelection()">← Terug</button>

      <!-- Leaderboard Section -->
      <div class="leaderboard-section">
        <div id="leaderboard">
          <!-- Leaderboard will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Multiplayer functionality removed for simplicity -->

    <footer class="footer">
      <p>
        Ontworpen met ❤️ door
        <a href="#" target="_blank">MorriQuiz Team</a>
      </p>
    </footer>
    
    <script src="js/accounts.js?v=16"></script>
    <script src="js/script.js?v=16"></script>
  </body>
</html>
