#!/usr/bin/env python3
"""
MorriQuiz Music Server
Serves the MorriQuiz website with proper directory listing support for music files.
"""

import http.server
import socketserver
import os
import json
import urllib.parse
from pathlib import Path

class MorriQuizHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler for MorriQuiz with music directory API support."""
    
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Handle API requests for music directory listings
        if self.path.startswith('/api/music/'):
            self.handle_music_api()
        else:
            # Serve regular files
            super().do_GET()
    
    def handle_music_api(self):
        """Handle API requests for music directory listings."""
        try:
            # Extract decade from path: /api/music/80s -> 80s
            path_parts = self.path.strip('/').split('/')
            if len(path_parts) >= 3:
                decade = path_parts[2]
                music_files = self.get_music_files(decade)
                
                # Return JSON response
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                
                response = {
                    'decade': decade,
                    'files': music_files,
                    'count': len(music_files)
                }
                
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            else:
                self.send_error(400, "Invalid API path")
                
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def get_music_files(self, decade):
        """Get list of MP3 files for a specific decade."""
        music_dir = Path(f"music/{decade}")
        
        if not music_dir.exists():
            raise FileNotFoundError(f"Music directory for {decade} not found")
        
        mp3_files = []
        
        # Scan for MP3 files
        for file_path in music_dir.glob("*.mp3"):
            if file_path.is_file():
                mp3_files.append(file_path.name)
        
        # Sort files alphabetically
        mp3_files.sort()
        
        return mp3_files
    
    def list_directory(self, path):
        """Override to provide better directory listings."""
        try:
            list_dir = os.listdir(path)
        except OSError:
            self.send_error(404, "No permission to list directory")
            return None
        
        list_dir.sort(key=lambda a: a.lower())
        
        # Generate HTML directory listing
        displaypath = urllib.parse.unquote(self.path, errors='surrogatepass')
        
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Directory listing for {displaypath}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1 {{ color: #667eea; }}
        .file {{ margin: 5px 0; }}
        .mp3 {{ color: #28a745; font-weight: bold; }}
        a {{ text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
    </style>
</head>
<body>
    <h1>Directory listing for {displaypath}</h1>
    <hr>
"""
        
        # Add parent directory link if not root
        if displaypath != '/':
            html_content += '<div class="file"><a href="../">📁 Parent Directory</a></div>\n'
        
        # Add files and directories
        for name in list_dir:
            fullname = os.path.join(path, name)
            linkname = name
            
            # Add trailing slash for directories
            if os.path.isdir(fullname):
                linkname = name + "/"
                icon = "📁"
                css_class = "dir"
            elif name.lower().endswith('.mp3'):
                icon = "🎵"
                css_class = "mp3"
            else:
                icon = "📄"
                css_class = "file"
            
            html_content += f'<div class="file {css_class}"><a href="{urllib.parse.quote(linkname)}">{icon} {name}</a></div>\n'
        
        html_content += """
    <hr>
    <p><em>MorriQuiz Music Server</em></p>
</body>
</html>
"""
        
        # Send response
        encoded = html_content.encode('utf-8', 'surrogateescape')
        self.send_response(200)
        self.send_header("Content-type", "text/html; charset=utf-8")
        self.send_header("Content-Length", str(len(encoded)))
        self.end_headers()
        self.wfile.write(encoded)
        
        return None

def main():
    """Start the MorriQuiz music server."""
    PORT = 8080
    
    print("=" * 50)
    print("🎵 MorriQuiz Music Server")
    print("=" * 50)
    print(f"Starting server on port {PORT}...")
    print(f"Server will serve files from: {os.getcwd()}")
    print()
    print("Access URLs:")
    print(f"  Local: http://localhost:{PORT}")
    print(f"  Network: http://0.0.0.0:{PORT}")
    print()
    print("API Endpoints:")
    print(f"  Music API: http://localhost:{PORT}/api/music/[decade]")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        with socketserver.TCPServer(("0.0.0.0", PORT), MorriQuizHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\nError starting server: {e}")

if __name__ == "__main__":
    main()
