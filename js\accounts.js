// MorriQuiz Account Management System
class AccountManager {
  constructor() {
    this.currentUser = null;
    this.sessionToken = null;
    this.apiBase = window.location.protocol + '//' + window.location.hostname + ':8081/api';
    this.useLocalStorage = false; // Fallback flag
  }

  // Make API request with localStorage fallback
  async apiRequest(endpoint, method = 'GET', data = null) {
    // If already using localStorage, skip API
    if (this.useLocalStorage) {
      throw new Error('Using localStorage fallback');
    }

    try {
      const options = {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        }
      };

      if (data) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(`${this.apiBase}${endpoint}`, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Server error');
      }

      return result;
    } catch (error) {
      console.error('API request failed, switching to localStorage:', error);
      this.useLocalStorage = true;
      throw new Error('Account server niet bereikbaar. Gebruik lokale opslag.');
    }
  }

  // localStorage fallback methods
  loadAccounts() {
    try {
      const saved = localStorage.getItem('morriQuizAccounts');
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('Error loading accounts:', error);
      return {};
    }
  }

  saveAccounts(accounts) {
    try {
      localStorage.setItem('morriQuizAccounts', JSON.stringify(accounts));
    } catch (error) {
      console.error('Error saving accounts:', error);
    }
  }

  // Hash password (simple hash for demo - in production use proper hashing)
  hashPassword(password) {
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Validate username
  validateUsername(username) {
    if (!username || username.length < 3) {
      return 'Gebruikersnaam moet minimaal 3 karakters zijn';
    }
    if (username.length > 20) {
      return 'Gebruikersnaam mag maximaal 20 karakters zijn';
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      return 'Gebruikersnaam mag alleen letters, cijfers, _ en - bevatten';
    }
    return null;
  }

  // Validate password
  validatePassword(password) {
    if (!password || password.length < 4) {
      return 'Wachtwoord moet minimaal 4 karakters zijn';
    }
    if (password.length > 50) {
      return 'Wachtwoord mag maximaal 50 karakters zijn';
    }
    return null;
  }

  // Create new account
  async createAccount(username, password) {
    const usernameError = this.validateUsername(username);
    if (usernameError) {
      throw new Error(usernameError);
    }

    const passwordError = this.validatePassword(password);
    if (passwordError) {
      throw new Error(passwordError);
    }

    try {
      const result = await this.apiRequest('/register', 'POST', {
        username: username,
        password: password
      });

      return result.account;
    } catch (error) {
      // Fallback to localStorage
      console.log('Using localStorage fallback for account creation');
      return this.createAccountLocal(username, password);
    }
  }

  // Local account creation fallback
  createAccountLocal(username, password) {
    const accounts = this.loadAccounts();
    const usernameKey = username.toLowerCase();

    if (accounts[usernameKey]) {
      throw new Error('Gebruikersnaam bestaat al');
    }

    const account = {
      username: username,
      passwordHash: this.hashPassword(password),
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      totalScore: 0,
      gamesPlayed: 0,
      bestScore: 0,
      bestDecade: null,
      totalPlayTime: 0,
      decadeScores: {},
      achievements: [],
      level: 1,
      experience: 0
    };

    accounts[usernameKey] = account;
    this.saveAccounts(accounts);

    return account;
  }

  // Login to account
  async login(username, password) {
    const usernameError = this.validateUsername(username);
    if (usernameError) {
      throw new Error(usernameError);
    }

    try {
      const result = await this.apiRequest('/login', 'POST', {
        username: username,
        password: password
      });

      // Set current user
      this.currentUser = result.account;
      this.sessionToken = this.generateSessionToken();
      this.saveSession();

      return result.account;
    } catch (error) {
      // Fallback to localStorage
      console.log('Using localStorage fallback for login');
      return this.loginLocal(username, password);
    }
  }

  // Local login fallback
  loginLocal(username, password) {
    const accounts = this.loadAccounts();
    const usernameKey = username.toLowerCase();

    if (!accounts[usernameKey]) {
      throw new Error('Gebruikersnaam of wachtwoord incorrect');
    }

    const account = accounts[usernameKey];
    const passwordHash = this.hashPassword(password);

    if (account.passwordHash !== passwordHash) {
      throw new Error('Gebruikersnaam of wachtwoord incorrect');
    }

    // Update last login
    account.lastLogin = new Date().toISOString();
    accounts[usernameKey] = account;
    this.saveAccounts(accounts);

    // Set current user
    this.currentUser = account;
    this.sessionToken = this.generateSessionToken();
    this.saveSession();

    return account;
  }

  // Generate session token
  generateSessionToken() {
    return btoa(this.currentUser.username + ':' + Date.now()).replace(/[^a-zA-Z0-9]/g, '');
  }

  // Save session
  saveSession() {
    if (this.currentUser && this.sessionToken) {
      const session = {
        username: this.currentUser.username,
        token: this.sessionToken,
        loginTime: new Date().toISOString()
      };
      localStorage.setItem('morriQuizSession', JSON.stringify(session));
    }
  }

  // Load session (only loads local session data, user data comes from server)
  loadSession() {
    try {
      const saved = localStorage.getItem('morriQuizSession');
      if (!saved) return false;

      const session = JSON.parse(saved);

      // Only restore session if it's recent (within 7 days)
      const sessionTime = new Date(session.loginTime);
      const now = new Date();
      const daysDiff = (now - sessionTime) / (1000 * 60 * 60 * 24);

      if (daysDiff > 7) {
        localStorage.removeItem('morriQuizSession');
        return false;
      }

      // Create a basic user object for session
      this.currentUser = {
        username: session.username,
        // Other data will be loaded when needed
      };
      this.sessionToken = session.token;
      return true;
    } catch (error) {
      console.error('Error loading session:', error);
    }
    return false;
  }

  // Logout
  logout() {
    this.currentUser = null;
    this.sessionToken = null;
    localStorage.removeItem('morriQuizSession');
  }

  // Check if user is logged in
  isLoggedIn() {
    return this.currentUser !== null;
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Update user stats
  async updateUserStats(score, decade, playTime = 0, correctAnswers = 0, totalQuestions = 100) {
    if (!this.currentUser) return;

    try {
      const result = await this.apiRequest('/update-stats', 'POST', {
        username: this.currentUser.username,
        score: score,
        decade: decade,
        playTime: playTime,
        correctAnswers: correctAnswers,
        totalQuestions: totalQuestions
      });

      // Update current user with new stats
      this.currentUser = result.account;
      this.saveSession();

      return result.account;
    } catch (error) {
      // Fallback to localStorage
      console.log('Using localStorage fallback for stats update');
      return this.updateUserStatsLocal(score, decade, playTime, correctAnswers, totalQuestions);
    }
  }

  // Local stats update fallback
  updateUserStatsLocal(score, decade, playTime = 0, correctAnswers = 0, totalQuestions = 100) {
    if (!this.currentUser) return;

    const accounts = this.loadAccounts();
    const usernameKey = this.currentUser.username.toLowerCase();
    const user = accounts[usernameKey];

    if (!user) return;

    // Update general stats
    user.totalScore += score;
    user.gamesPlayed += 1;
    user.totalPlayTime = (user.totalPlayTime || 0) + playTime;
    user.lastPlayed = new Date().toISOString();

    // Calculate experience
    const experienceGained = Math.floor(score / 10) + correctAnswers;
    user.experience = (user.experience || 0) + experienceGained;

    // Calculate level
    const newLevel = Math.floor(user.experience / 1000) + 1;
    user.level = newLevel;

    // Update decade stats
    if (!user.decadeScores) {
      user.decadeScores = {};
    }

    if (!user.decadeScores[decade]) {
      user.decadeScores[decade] = {
        totalScore: 0,
        gamesPlayed: 0,
        bestScore: 0,
        totalPlayTime: 0,
        totalCorrectAnswers: 0,
        totalQuestions: 0,
        games: []
      };
    }

    const decadeStats = user.decadeScores[decade];
    decadeStats.totalScore += score;
    decadeStats.gamesPlayed += 1;
    decadeStats.totalPlayTime += playTime;
    decadeStats.totalCorrectAnswers += correctAnswers;
    decadeStats.totalQuestions += totalQuestions;

    // Add game to history
    decadeStats.games.push({
      score: score,
      correctAnswers: correctAnswers,
      totalQuestions: totalQuestions,
      playTime: playTime,
      date: new Date().toISOString()
    });

    // Update best scores
    if (score > decadeStats.bestScore) {
      decadeStats.bestScore = score;
    }

    if (score > (user.bestScore || 0)) {
      user.bestScore = score;
      user.bestDecade = decade;
    }

    // Check achievements
    this.checkAchievementsLocal(user, score, decade, correctAnswers, totalQuestions);

    // Save to localStorage
    accounts[usernameKey] = user;
    this.saveAccounts(accounts);

    // Update current user
    this.currentUser = user;
    this.saveSession();

    return user;
  }

  // Local achievements check
  checkAchievementsLocal(user, score, decade, correctAnswers, totalQuestions) {
    const achievements = user.achievements || [];
    const newAchievements = [];

    // First game achievement
    if (user.gamesPlayed === 1 && !achievements.includes('first_game')) {
      achievements.push('first_game');
      newAchievements.push('Eerste Quiz Voltooid!');
    }

    // Perfect score achievement
    if (correctAnswers === totalQuestions && !achievements.includes('perfect_score')) {
      achievements.push('perfect_score');
      newAchievements.push('Perfecte Score!');
    }

    // High score achievement
    if (score >= 80 && !achievements.includes('high_score')) {
      achievements.push('high_score');
      newAchievements.push('Hoge Score! (80+ punten)');
    }

    // Decade master (play all decades)
    const playedDecades = Object.keys(user.decadeScores || {});
    if (playedDecades.length >= 4 && !achievements.includes('decade_master')) {
      achievements.push('decade_master');
      newAchievements.push('Decade Master! (4+ decades gespeeld)');
    }

    // Dedicated player (10+ games)
    if (user.gamesPlayed >= 10 && !achievements.includes('dedicated_player')) {
      achievements.push('dedicated_player');
      newAchievements.push('Toegewijde Speler! (10+ games)');
    }

    user.achievements = achievements;

    // Show achievement notifications
    if (newAchievements.length > 0) {
      this.showAchievementNotifications(newAchievements);
    }
  }

  // Check for achievements
  checkAchievements(user, score, decade, correctAnswers, totalQuestions) {
    const achievements = user.achievements || [];
    const newAchievements = [];

    // First game achievement
    if (user.gamesPlayed === 1 && !achievements.includes('first_game')) {
      achievements.push('first_game');
      newAchievements.push('Eerste Quiz Voltooid!');
    }

    // Perfect score achievement
    if (correctAnswers === totalQuestions && !achievements.includes('perfect_score')) {
      achievements.push('perfect_score');
      newAchievements.push('Perfecte Score!');
    }

    // High score achievement
    if (score >= 80 && !achievements.includes('high_score')) {
      achievements.push('high_score');
      newAchievements.push('Hoge Score! (80+ punten)');
    }

    // Decade master (play all decades)
    const playedDecades = Object.keys(user.decadeScores || {});
    if (playedDecades.length >= 4 && !achievements.includes('decade_master')) {
      achievements.push('decade_master');
      newAchievements.push('Decade Master! (4+ decades gespeeld)');
    }

    // Dedicated player (10+ games)
    if (user.gamesPlayed >= 10 && !achievements.includes('dedicated_player')) {
      achievements.push('dedicated_player');
      newAchievements.push('Toegewijde Speler! (10+ games)');
    }

    user.achievements = achievements;

    // Show achievement notifications
    if (newAchievements.length > 0) {
      this.showAchievementNotifications(newAchievements);
    }
  }

  // Show achievement notifications
  showAchievementNotifications(achievements) {
    achievements.forEach((achievement, index) => {
      setTimeout(() => {
        this.showNotification(`🏆 ${achievement}`, 'achievement');
      }, index * 1000);
    });
  }

  // Show notification
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'achievement' ? '#ffd700' : '#4facfe'};
      color: ${type === 'achievement' ? '#000' : '#fff'};
      padding: 15px 20px;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      z-index: 10000;
      font-weight: bold;
      animation: slideIn 0.3s ease;
    `;

    // Add CSS animation
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remove after 4 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }

  // Get user level info
  getUserLevelInfo(user = this.currentUser) {
    if (!user) return null;

    const currentXP = user.experience || 0;
    const currentLevel = user.level || 1;
    const xpForCurrentLevel = (currentLevel - 1) * 1000;
    const xpForNextLevel = currentLevel * 1000;
    const xpProgress = currentXP - xpForCurrentLevel;
    const xpNeeded = xpForNextLevel - currentXP;

    return {
      level: currentLevel,
      experience: currentXP,
      xpProgress: xpProgress,
      xpNeeded: xpNeeded,
      progressPercentage: Math.round((xpProgress / 1000) * 100)
    };
  }

  // Get leaderboard
  async getLeaderboard() {
    try {
      const result = await this.apiRequest('/leaderboard', 'GET');

      // Mark current user
      return result.leaderboard.map(user => ({
        ...user,
        isCurrentUser: this.currentUser && user.username === this.currentUser.username
      }));
    } catch (error) {
      // Fallback to localStorage
      console.log('Using localStorage fallback for leaderboard');
      return this.getLeaderboardLocal();
    }
  }

  // Local leaderboard fallback
  getLeaderboardLocal() {
    const accounts = this.loadAccounts();
    const users = Object.values(accounts).filter(user => user.gamesPlayed > 0);
    users.sort((a, b) => (b.totalScore || 0) - (a.totalScore || 0));

    return users.slice(0, 10).map((user, index) => ({
      rank: index + 1,
      username: user.username,
      totalScore: user.totalScore || 0,
      gamesPlayed: user.gamesPlayed || 0,
      level: user.level || 1,
      bestScore: user.bestScore || 0,
      bestDecade: user.bestDecade,
      isCurrentUser: this.currentUser && user.username === this.currentUser.username
    }));
  }
}

// Global account manager instance
window.AccountManager = new AccountManager();

// Debug: Log that AccountManager is loaded
console.log('✅ AccountManager loaded successfully');

// Dispatch event to notify other scripts
window.dispatchEvent(new CustomEvent('accountManagerReady'));
