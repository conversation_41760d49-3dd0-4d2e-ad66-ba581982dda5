/* Multiplayer Quiz Specific Styles */

/* Header Enhancements */
.room-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.room-code {
  background: rgba(102, 126, 234, 0.1);
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  color: #667eea;
  font-size: 0.9rem;
}

.question-counter {
  color: #666;
  font-size: 0.8rem;
}

/* Players Status Section */
.players-status {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin-top: 20px;
  backdrop-filter: blur(10px);
}

.players-status h3 {
  color: #667eea;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.1rem;
}

.player-answer-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  margin: 8px 0;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.player-answer-status.answered {
  background: rgba(34, 197, 94, 0.1);
  border-left: 4px solid #22c55e;
}

.player-answer-status.waiting {
  background: rgba(251, 191, 36, 0.1);
  border-left: 4px solid #fbbf24;
}

.player-answer-status.current-player {
  border: 2px solid #667eea;
}

.player-name-status {
  font-weight: bold;
  color: #333;
}

.answer-status-icon {
  font-size: 1.2rem;
}

/* Waiting Screen */
.waiting-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.waiting-content {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.waiting-spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin: 20px 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Results Screen */
.results-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.results-content {
  background: white;
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.question-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  margin: 8px 0;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.05);
}

.question-result-item.correct {
  background: rgba(34, 197, 94, 0.1);
  border-left: 4px solid #22c55e;
}

.question-result-item.incorrect {
  background: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #ef4444;
}

.result-player-name {
  font-weight: bold;
}

.result-answer {
  font-size: 0.9rem;
  color: #666;
}

.result-points {
  font-weight: bold;
  color: #22c55e;
}

.next-question-timer {
  margin-top: 20px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  color: #667eea;
  font-weight: bold;
}

/* Final Results */
.final-results-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.final-results-content {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.final-leaderboard-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  margin: 10px 0;
  border-radius: 15px;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.final-leaderboard-item.first-place {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border: 2px solid #f59e0b;
}

.final-leaderboard-item.second-place {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
}

.final-leaderboard-item.third-place {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
}

.final-rank {
  font-size: 1.5rem;
  font-weight: bold;
  width: 40px;
  text-align: center;
}

.final-player-info {
  flex-grow: 1;
  text-align: left;
  margin-left: 15px;
}

.final-player-name {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 3px;
}

.final-player-score {
  font-size: 1rem;
  color: #666;
}

.final-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.return-btn, .home-btn {
  flex: 1;
  padding: 15px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.return-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.home-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.return-btn:hover, .home-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .room-info {
    align-items: center;
    text-align: center;
  }
  
  .players-status {
    padding: 15px;
  }
  
  .waiting-content, .results-content, .final-results-content {
    padding: 20px;
    margin: 10px;
  }
  
  .final-actions {
    flex-direction: column;
  }
}
