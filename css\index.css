body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding-bottom: 60px;
}

/* Loader Styling */
#loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 8px solid #fff;
  animation: l20-1 0.8s infinite linear alternate,
    l20-2 1.6s infinite linear;
}

@keyframes l20-1 {
  0% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
  }
  12.5% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
  }
  25% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%);
  }
  50% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
  }
  62.5% {
    clip-path: polygon(50% 50%, 100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
  }
  75% {
    clip-path: polygon(50% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%);
  }
  100% {
    clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%);
  }
}

@keyframes l20-2 {
  0% { transform: scaleY(1) rotate(0deg); }
  49.99% { transform: scaleY(1) rotate(135deg); }
  50% { transform: scaleY(-1) rotate(0deg); }
  100% { transform: scaleY(-1) rotate(-135deg); }
}

.container {
  width: 100%;
  max-width: 800px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 30px 40px;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin: 20px;
}

.hidden {
  display: none !important;
}

/* Logo styling */
.logo-container {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  max-width: 250px;
  max-height: 250px;
  width: auto;
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

h1 {
  color: #667eea;
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #555;
  margin-bottom: 30px;
}

/* Naam invoer sectie */
.name-input-section {
  margin: 30px 0;
}

.name-input-section h3 {
  color: #667eea;
  margin-bottom: 15px;
}

#player-name {
  width: 300px;
  max-width: 100%;
  padding: 15px;
  font-size: 1.1rem;
  border: 2px solid #ddd;
  border-radius: 25px;
  margin-bottom: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

#player-name:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

#start-quiz-btn {
  display: inline-block;
  width: 200px;
  padding: 15px;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
}

#start-quiz-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

#start-quiz-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Decade grid */
.decade-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.decade-btn {
  padding: 25px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.decade-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.decade-year {
  font-size: 2rem;
  font-weight: bold;
}

.decade-desc {
  font-size: 0.9rem;
  opacity: 0.9;
}

.progress-indicator {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 5px 10px;
  margin-top: 8px;
  font-size: 0.8rem !important;
  color: #fff !important;
  opacity: 0.9 !important;
}

.decade-btn:hover .progress-indicator {
  background: rgba(255, 255, 255, 0.3);
}

/* Leaderboard Styling */
.leaderboard-list {
  margin-top: 20px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  margin: 10px 0;
  padding: 15px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.leaderboard-item:hover {
  transform: translateX(5px);
}

.leaderboard-item.first-place {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border: 2px solid #f59e0b;
}

.rank {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  width: 40px;
  text-align: center;
  margin-right: 15px;
}

.first-place .rank {
  color: #92400e;
  font-size: 1.8rem;
}

.player-info {
  flex-grow: 1;
}

.player-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.player-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 5px;
  flex-wrap: wrap;
}

.player-stats span {
  background: rgba(102, 126, 234, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
}

.player-details {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.player-details span {
  font-size: 0.8rem;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 8px;
}

.total-score {
  font-weight: bold !important;
  background: rgba(34, 197, 94, 0.1) !important;
  color: #16a34a !important;
}

.play-time {
  background: rgba(168, 85, 247, 0.1) !important;
  color: #7c3aed !important;
}

/* Leaderboard Section */
.leaderboard-section {
  margin-top: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.leaderboard-section h3 {
  text-align: center;
  color: #667eea;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

/* Game Mode Selection */
.game-mode-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 30px 0;
}

.mode-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20px;
  padding: 30px 20px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.mode-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.mode-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
}

.mode-title {
  font-size: 1.3rem;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.mode-desc {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Multiplayer Options */
.multiplayer-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 30px 0;
}

.multiplayer-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  border-radius: 15px;
  padding: 25px 15px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.multiplayer-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(245, 87, 108, 0.3);
}

.mp-icon {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 8px;
}

.mp-title {
  font-size: 1.1rem;
  font-weight: bold;
  display: block;
  margin-bottom: 3px;
}

.mp-desc {
  font-size: 0.8rem;
  opacity: 0.9;
}

/* Room Creation & Joining */
.create-room-form, .join-room-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.create-room-form label, .join-room-form label {
  display: block;
  color: #667eea;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.decade-select, .room-code-input {
  width: 100%;
  padding: 15px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 20px;
  transition: border-color 0.3s ease;
}

.decade-select:focus, .room-code-input:focus {
  outline: none;
  border-color: #667eea;
}

.room-code-input {
  text-align: center;
  font-size: 1.5rem;
  font-weight: bold;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.create-btn, .join-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 15px;
  padding: 15px 30px;
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.create-btn:hover, .join-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

/* Multiplayer Lobby */
.lobby-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 25px;
  border-radius: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.decade-info {
  text-align: center;
  margin-bottom: 25px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
}

.decade-label {
  color: #667eea;
  font-weight: bold;
  margin-right: 10px;
}

.decade-value {
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
}

.players-list h3 {
  color: #667eea;
  margin-bottom: 15px;
  text-align: center;
}

.player-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  margin: 8px 0;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.player-item.ready {
  background: rgba(34, 197, 94, 0.1);
  border-left: 4px solid #22c55e;
}

.player-item.not-ready {
  background: rgba(251, 191, 36, 0.1);
  border-left: 4px solid #fbbf24;
}

.player-name {
  font-weight: bold;
  color: #333;
}

.player-status {
  font-size: 0.9rem;
}

.host-badge {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  padding: 3px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: bold;
}

.lobby-controls {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.ready-btn, .start-btn {
  flex: 1;
  padding: 15px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ready-btn {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.ready-btn.ready {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.start-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.ready-btn:hover, .start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Rules sectie */
.rules {
  text-align: left;
  margin: 30px 0;
  background: rgba(102, 126, 234, 0.1);
  padding: 20px;
  border-radius: 15px;
}

.rules h3 {
  color: #667eea;
  text-align: center;
  margin-bottom: 15px;
}

.rules ul {
  list-style-type: none;
  padding: 0;
}

.rules li {
  margin: 12px 0;
  padding-left: 25px;
  position: relative;
  line-height: 1.5;
}

.rules li:before {
  content: "🎵";
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

/* Leaderboard sectie */
.leaderboard-section {
  margin: 30px 0;
  background: rgba(255, 215, 0, 0.1);
  padding: 20px;
  border-radius: 15px;
}

.leaderboard-section h3 {
  color: #667eea;
  margin-bottom: 15px;
}

#leaderboard-list {
  max-height: 200px;
  overflow-y: auto;
}

.leaderboard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin: 5px 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.leaderboard-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(5px);
}

.leaderboard-rank {
  font-weight: bold;
  color: #667eea;
  min-width: 30px;
}

.leaderboard-name {
  flex-grow: 1;
  text-align: left;
  margin-left: 15px;
}

.leaderboard-score {
  font-weight: bold;
  color: #764ba2;
}

/* Back button */
#back-to-name {
  margin-top: 20px;
  padding: 10px 20px;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

#back-to-name:hover {
  background: #667eea;
  color: white;
}

.conducted-by {
  margin-top: 30px;
  font-style: italic;
  color: #666;
}

/* Footer */
.footer {
  width: 100%;
  background: rgba(51, 51, 51, 0.9);
  color: #ffffff;
  text-align: center;
  padding: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  backdrop-filter: blur(10px);
}

.footer p {
  margin: 0;
  font-size: 0.9em;
  color: #bbb;
}

.footer a {
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 10px;
    padding: 20px;
  }

  .logo {
    max-width: 180px;
    max-height: 180px;
  }

  h1 {
    font-size: 2rem;
  }

  .decade-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  #player-name {
    width: 100%;
  }
}

/* Account System Styling */
.auth-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.auth-form h3 {
  color: #667eea;
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.auth-form .input-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.auth-form input {
  padding: 15px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.auth-form input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.auth-form button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  padding: 15px;
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.auth-form button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-switch {
  text-align: center;
  margin-top: 15px;
  color: #666;
}

.auth-switch a {
  color: #667eea;
  text-decoration: none;
  font-weight: bold;
}

.auth-switch a:hover {
  text-decoration: underline;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 2px solid #ef4444;
  color: #ef4444;
  padding: 15px;
  border-radius: 10px;
  margin: 15px 0;
  text-align: center;
  font-weight: bold;
}

/* User Dashboard */
.user-dashboard {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.user-welcome h3 {
  color: #667eea;
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.stat-item {
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 10px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
}

.level-progress {
  margin: 20px 0;
  text-align: center;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  height: 20px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
  width: 0%;
}

.xp-text {
  color: #667eea;
  font-weight: bold;
  font-size: 0.9rem;
}

.start-quiz-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 15px;
  padding: 15px 30px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin: 10px 0;
}

.start-quiz-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.logout-btn {
  background: rgba(239, 68, 68, 0.8);
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: translateY(-1px);
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.benefit-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 10px;
  gap: 15px;
}

.benefit-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.benefit-text strong {
  display: block;
  color: #667eea;
  margin-bottom: 5px;
}

.benefit-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Account Leaderboard Enhancements */
.leaderboard-item.current-user {
  border: 2px solid #4facfe;
  background: rgba(79, 172, 254, 0.1);
}

.you-badge {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 5px;
}

.level-badge {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #92400e;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 5px;
}

/* Loading Indicator */
.loading-indicator {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(102, 126, 234, 0.3);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-indicator p {
  color: #667eea;
  margin: 0;
  font-weight: bold;
}
