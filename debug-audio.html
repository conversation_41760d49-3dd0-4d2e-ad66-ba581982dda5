<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MorriQuiz Audio Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .file-test { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .testing { background: #fff3cd; color: #856404; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        audio { width: 100%; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Audio Debug Tool</h1>
        <p>Deze tool test of alle muziekbestanden correct kunnen worden geladen.</p>
        
        <button onclick="testAllFiles()">Test Alle Bestanden</button>
        <button onclick="testRandomFile()">Test Random Bestand</button>
        <button onclick="clearResults()">Wis Resultaten</button>
        
        <div id="results"></div>
    </div>
    
    <div class="container">
        <h3>Test Audio Player</h3>
        <input type="text" id="test-path" placeholder="music/10s/bestandsnaam.mp3" style="width: 300px; padding: 5px;">
        <button onclick="testSpecificFile()">Test Dit Bestand</button>
        
        <audio id="test-audio" controls style="width: 100%; margin-top: 10px;">
            Je browser ondersteunt geen audio.
        </audio>
        
        <div id="test-result"></div>
    </div>

    <script>
        let allFiles = [];
        
        async function loadMusicFiles() {
            const decades = ['60s', '70s', '80s', '90s', '00s', '10s', '20s'];
            allFiles = [];
            
            for (const decade of decades) {
                try {
                    const response = await fetch(`/api/music/${decade}`);
                    if (response.ok) {
                        const data = await response.json();
                        data.files.forEach(file => {
                            allFiles.push({
                                decade: decade,
                                filename: file,
                                path: `music/${decade}/${file}`
                            });
                        });
                    }
                } catch (error) {
                    console.error(`Error loading ${decade}:`, error);
                }
            }
            
            return allFiles;
        }
        
        async function testAllFiles() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="testing">🔍 Laden van alle bestanden...</div>';
            
            await loadMusicFiles();
            
            if (allFiles.length === 0) {
                results.innerHTML = '<div class="error">❌ Geen muziekbestanden gevonden!</div>';
                return;
            }
            
            results.innerHTML = `<div class="testing">🔍 Testen van ${allFiles.length} bestanden...</div>`;
            
            let successCount = 0;
            let errorCount = 0;
            
            for (let i = 0; i < Math.min(allFiles.length, 10); i++) {
                const file = allFiles[i];
                const testResult = await testAudioFile(file.path);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `file-test ${testResult.success ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    ${testResult.success ? '✅' : '❌'} ${file.decade}: ${file.filename}
                    <br><small>${testResult.message}</small>
                `;
                results.appendChild(resultDiv);
                
                if (testResult.success) successCount++;
                else errorCount++;
            }
            
            const summary = document.createElement('div');
            summary.className = 'container';
            summary.innerHTML = `
                <h3>Test Resultaten:</h3>
                <p>✅ Succesvol: ${successCount}</p>
                <p>❌ Gefaald: ${errorCount}</p>
                <p>📊 Totaal getest: ${Math.min(allFiles.length, 10)} van ${allFiles.length}</p>
            `;
            results.appendChild(summary);
        }
        
        async function testRandomFile() {
            await loadMusicFiles();
            
            if (allFiles.length === 0) {
                document.getElementById('results').innerHTML = '<div class="error">❌ Geen bestanden om te testen!</div>';
                return;
            }
            
            const randomFile = allFiles[Math.floor(Math.random() * allFiles.length)];
            const testResult = await testAudioFile(randomFile.path);
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="file-test ${testResult.success ? 'success' : 'error'}">
                    <h4>Random Test:</h4>
                    ${testResult.success ? '✅' : '❌'} ${randomFile.decade}: ${randomFile.filename}
                    <br><small>${testResult.message}</small>
                    ${testResult.success ? `<audio controls src="${randomFile.path}" style="width: 100%; margin-top: 10px;"></audio>` : ''}
                </div>
            `;
        }
        
        async function testAudioFile(path) {
            return new Promise((resolve) => {
                const audio = new Audio();
                
                const timeout = setTimeout(() => {
                    resolve({
                        success: false,
                        message: 'Timeout - bestand laadt te langzaam'
                    });
                }, 5000);
                
                audio.oncanplay = () => {
                    clearTimeout(timeout);
                    resolve({
                        success: true,
                        message: 'Bestand kan worden afgespeeld'
                    });
                };
                
                audio.onerror = (error) => {
                    clearTimeout(timeout);
                    resolve({
                        success: false,
                        message: `Fout: ${error.type || 'Kan bestand niet laden'}`
                    });
                };
                
                audio.src = path;
            });
        }
        
        async function testSpecificFile() {
            const path = document.getElementById('test-path').value;
            const audio = document.getElementById('test-audio');
            const result = document.getElementById('test-result');
            
            if (!path) {
                result.innerHTML = '<div class="error">Voer een bestandspad in!</div>';
                return;
            }
            
            result.innerHTML = '<div class="testing">🔍 Testen...</div>';
            
            audio.src = path;
            
            const testResult = await testAudioFile(path);
            result.innerHTML = `
                <div class="file-test ${testResult.success ? 'success' : 'error'}">
                    ${testResult.success ? '✅' : '❌'} ${testResult.message}
                </div>
            `;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('test-result').innerHTML = '';
        }
        
        // Auto-load files on page load
        window.addEventListener('load', loadMusicFiles);
    </script>
</body>
</html>
