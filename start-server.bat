@echo off
title MorriQuiz Server v2.0
color 0A
cls
echo.
echo  ███╗   ███╗ ██████╗ ██████╗ ██████╗ ██╗ ██████╗ ██╗   ██╗██╗███████╗
echo  ████╗ ████║██╔═══██╗██╔══██╗██╔══██╗██║██╔═══██╗██║   ██║██║╚══███╔╝
echo  ██╔████╔██║██║   ██║██████╔╝██████╔╝██║██║   ██║██║   ██║██║  ███╔╝
echo  ██║╚██╔╝██║██║   ██║██╔══██╗██╔══██╗██║██║▄▄ ██║██║   ██║██║ ███╔╝
echo  ██║ ╚═╝ ██║╚██████╔╝██║  ██║██║  ██║██║╚██████╔╝╚██████╔╝██║███████╗
echo  ╚═╝     ╚═╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝ ╚══▀▀═╝  ╚═════╝ ╚═╝╚══════╝
echo.
echo  🎵 Muziek Quiz Server v2.0 - Multiplayer Edition
echo  ═══════════════════════════════════════════════════════════════════════
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo  ❌ ERROR: Python not found!
    echo  Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

echo  📊 System Check: Python ✅
echo.

REM Get Tailscale IP
echo  🔍 Checking network configuration...
for /f "tokens=1" %%i in ('tailscale status 2^>nul ^| findstr /C:"morflix"') do set TAILSCALE_IP=%%i

if "%TAILSCALE_IP%"=="" (
    echo  ⚠️  Tailscale: Not connected
    set TAILSCALE_IP=Not Available
) else (
    echo  🔗 Tailscale: Connected (%TAILSCALE_IP%)
)

REM Get local IP
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /C:"IPv4 Address"') do set LOCAL_IP=%%i
set LOCAL_IP=%LOCAL_IP: =%
echo  🌐 Local Network: %LOCAL_IP%
echo.

echo  🎮 Game Features:
echo  ├── 👤 Singleplayer: 100-question quiz per decade
echo  ├── 👥 Multiplayer: 20-question competitive quiz (2-4 players)
echo  ├── 🏆 Leaderboard with statistics and play time tracking
echo  ├── 📊 Per-decade performance analytics
echo  └── 🎵 Real-time audio streaming
echo.

echo  📁 Music Library Status:
if exist "music\60s" (echo  ├── 60s: ✅ Available) else (echo  ├── 60s: ❌ Missing)
if exist "music\70s" (echo  ├── 70s: ✅ Available) else (echo  ├── 70s: ❌ Missing)
if exist "music\80s" (echo  ├── 80s: ✅ Available) else (echo  ├── 80s: ❌ Missing)
if exist "music\90s" (echo  ├── 90s: ✅ Available) else (echo  ├── 90s: ❌ Missing)
if exist "music\00s" (echo  ├── 00s: ✅ Available) else (echo  ├── 00s: ❌ Missing)
if exist "music\10s" (echo  ├── 10s: ✅ Available) else (echo  ├── 10s: ❌ Missing)
if exist "music\20s" (echo  └── 20s: ✅ Available) else (echo  └── 20s: ❌ Missing)
echo.

echo  🌐 Access URLs:
echo  ═══════════════════════════════════════════════════════════════════════
echo  📱 Local Access:
echo     http://localhost:8080
echo     http://%LOCAL_IP%:8080
echo.
echo  🔗 Network Access:
if not "%TAILSCALE_IP%"=="Not Available" (
    echo     http://%TAILSCALE_IP%:8080 (Tailscale)
) else (
    echo     Tailscale not available
)
echo.
echo  ═══════════════════════════════════════════════════════════════════════
echo  🚀 Starting server on port 8080...
echo  📊 Server Status: RUNNING ✅
echo  🕐 Started: %date% %time%
echo.
echo  💡 Tips:
echo  ├── Use Ctrl+C to stop the server
echo  ├── Share the Tailscale URL for remote access
echo  ├── Check firewall if local network access fails
echo  └── Use convert-mp3-length.bat to optimize audio files
echo.
echo  ═══════════════════════════════════════════════════════════════════════
echo.

REM Start the server
if exist music-server.py (
    echo  🎵 Starting MorriQuiz Music Server with API support...
    python music-server.py
) else (
    echo  🌐 Starting basic HTTP server...
    python -m http.server 8080 --bind 0.0.0.0
)
