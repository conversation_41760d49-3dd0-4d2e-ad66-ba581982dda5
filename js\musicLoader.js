// MorriQuiz Music Loader
// Handles loading and parsing music files from decade directories

class MusicLoader {
  constructor() {
    this.musicDatabase = {};
    this.supportedFormats = ['.mp3'];
  }

  // Load music files for a specific decade
  async loadDecadeMusic(decade) {
    try {
      // For development/demo purposes, we'll use a predefined list
      // In production, this would scan the actual directory
      const musicList = await this.getMusicList(decade);
      
      if (musicList.length === 0) {
        throw new Error(`Geen muziekbestanden gevonden voor ${decade}`);
      }

      this.musicDatabase[decade] = musicList.map(filename => this.parseFilename(filename, decade));
      return this.musicDatabase[decade];
    } catch (error) {
      console.error(`Fout bij laden van muziek voor ${decade}:`, error);
      throw error;
    }
  }

  // Parse filename to extract artist, title, and year
  parseFilename(filename, decade) {
    // Expected format: "Artist - Title - Year.mp3"
    const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
    const parts = nameWithoutExt.split(' - ');

    if (parts.length < 3) {
      console.warn(`Ongeldige bestandsnaam: ${filename}. Verwacht formaat: "Artiest - Titel - Jaar.mp3"`);
      return null;
    }

    // Handle files with extra information (more than 3 parts)
    let artist, title, year;

    if (parts.length === 3) {
      // Standard format: Artist - Title - Year
      [artist, title, year] = parts;
    } else {
      // Extended format: Artist - Title - Extra Info - Year
      // Take first part as artist, last part as year, combine middle parts as title
      artist = parts[0];
      year = parts[parts.length - 1];
      title = parts.slice(1, -1).join(' - ');
    }

    // Validate year
    const yearNum = parseInt(year.trim());
    if (isNaN(yearNum) || yearNum < 1900 || yearNum > 2030) {
      console.warn(`Ongeldige jaar in bestandsnaam: ${filename}`);
      return null;
    }

    return {
      filename: filename,
      artist: artist.trim(),
      title: title.trim(),
      year: yearNum,
      decade: decade,
      path: `/music/${decade}/${filename}` // Absolute path from server root
    };
  }

  // Get list of music files for a decade (scan actual directory)
  async getMusicList(decade) {
    try {
      const actualFiles = await this.scanMusicDirectory(decade);
      if (actualFiles.length > 0) {
        console.log(`Gevonden ${actualFiles.length} muziekbestanden voor ${decade}`);
        return actualFiles;
      } else {
        throw new Error(`Geen MP3 bestanden gevonden in music/${decade}/`);
      }
    } catch (error) {
      console.error(`Fout bij laden van muziek voor ${decade}:`, error);
      throw new Error(`Geen muziekbestanden gevonden voor ${decade}. Zorg ervoor dat je MP3 bestanden in de map 'music/${decade}' hebt geplaatst met het formaat: 'Artiest - Titel - Jaar.mp3'`);
    }
  }

  // Scan actual music directory for MP3 files
  async scanMusicDirectory(decade) {
    try {
      // First try the API endpoint
      const apiResponse = await fetch(`/api/music/${decade}`);

      if (apiResponse.ok) {
        const data = await apiResponse.json();
        if (data.files && data.files.length > 0) {
          console.log(`API: Gevonden ${data.files.length} bestanden voor ${decade}`);
          return data.files;
        }
      }

      // Fallback: try to fetch the directory listing directly
      const response = await fetch(`music/${decade}/`);

      if (!response.ok) {
        throw new Error(`Directory music/${decade}/ not accessible`);
      }

      const html = await response.text();

      // Parse HTML directory listing to extract MP3 files
      const mp3Files = this.parseDirectoryListing(html);

      if (mp3Files.length === 0) {
        throw new Error(`Geen MP3 bestanden gevonden in music/${decade}/`);
      }

      console.log(`HTML: Gevonden ${mp3Files.length} bestanden voor ${decade}`);
      return mp3Files;
    } catch (error) {
      console.error(`Fout bij scannen van music/${decade}/:`, error);
      throw error;
    }
  }

  // Parse directory listing HTML to extract MP3 files
  parseDirectoryListing(html) {
    const mp3Files = [];

    // Create a temporary DOM element to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Look for links to MP3 files
    const links = doc.querySelectorAll('a[href]');

    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href.toLowerCase().endsWith('.mp3')) {
        // Decode URL encoding
        const filename = decodeURIComponent(href);
        mp3Files.push(filename);
      }
    });

    return mp3Files;
  }

  // Get random songs for quiz questions
  getRandomSongs(decade, count = 100) {
    const songs = this.musicDatabase[decade];
    if (!songs || songs.length === 0) {
      throw new Error(`Geen muziek beschikbaar voor ${decade}`);
    }

    // For 100 questions, we need to repeat songs if we don't have enough
    const availableSongs = songs.filter(song => song !== null);
    if (availableSongs.length === 0) {
      throw new Error(`Geen geldige muziek beschikbaar voor ${decade}`);
    }

    const selectedSongs = [];

    // If we have enough songs, shuffle and take the requested amount
    if (availableSongs.length >= count) {
      const shuffled = [...availableSongs].sort(() => 0.5 - Math.random());
      return shuffled.slice(0, count);
    }

    // If we need more songs than available, repeat songs with different question types
    while (selectedSongs.length < count) {
      const shuffled = [...availableSongs].sort(() => 0.5 - Math.random());
      selectedSongs.push(...shuffled);
    }

    return selectedSongs.slice(0, count);
  }

  // Generate quiz questions from songs
  generateQuestions(songs) {
    const questions = [];
    const questionTypes = ['artist', 'title', 'year'];

    songs.forEach((song, index) => {
      const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)];
      const question = this.createQuestion(song, questionType, songs, index + 1);
      if (question) {
        questions.push(question);
      }
    });

    return questions;
  }

  // Create a single question
  createQuestion(song, type, allSongs, questionNumber) {
    let questionText, correctAnswer, options;

    switch (type) {
      case 'artist':
        questionText = `Vraag ${questionNumber}: Wie is de artiest van dit nummer?`;
        correctAnswer = song.artist;
        options = this.generateArtistOptions(song, allSongs);
        break;
      case 'title':
        questionText = `Vraag ${questionNumber}: Wat is de titel van dit nummer?`;
        correctAnswer = song.title;
        options = this.generateTitleOptions(song, allSongs);
        break;
      case 'year':
        questionText = `Vraag ${questionNumber}: In welk jaar werd dit nummer uitgebracht?`;
        correctAnswer = song.year.toString();
        options = this.generateYearOptions(song);
        break;
      default:
        return null;
    }

    return {
      questionNumber,
      question: questionText,
      song: song,
      type: type,
      options: this.shuffleArray(options),
      correct: correctAnswer,
      explanation: `"${song.title}" werd uitgebracht door ${song.artist} in ${song.year}.`
    };
  }

  // Generate artist options (3 wrong + 1 correct)
  generateArtistOptions(correctSong, allSongs) {
    const options = [correctSong.artist];
    const otherArtists = allSongs
      .filter(song => song.artist !== correctSong.artist)
      .map(song => song.artist);
    
    // Add 3 random wrong artists
    while (options.length < 4 && otherArtists.length > 0) {
      const randomIndex = Math.floor(Math.random() * otherArtists.length);
      const artist = otherArtists.splice(randomIndex, 1)[0];
      if (!options.includes(artist)) {
        options.push(artist);
      }
    }

    // Fill with generic options if needed
    const genericArtists = ['The Beatles', 'Queen', 'Elvis Presley', 'Madonna', 'Michael Jackson'];
    while (options.length < 4) {
      const generic = genericArtists[Math.floor(Math.random() * genericArtists.length)];
      if (!options.includes(generic)) {
        options.push(generic);
      }
    }

    return options;
  }

  // Generate title options (3 wrong + 1 correct)
  generateTitleOptions(correctSong, allSongs) {
    const options = [correctSong.title];
    const otherTitles = allSongs
      .filter(song => song.title !== correctSong.title)
      .map(song => song.title);
    
    // Add 3 random wrong titles
    while (options.length < 4 && otherTitles.length > 0) {
      const randomIndex = Math.floor(Math.random() * otherTitles.length);
      const title = otherTitles.splice(randomIndex, 1)[0];
      if (!options.includes(title)) {
        options.push(title);
      }
    }

    return options;
  }

  // Generate year options (3 wrong + 1 correct)
  generateYearOptions(correctSong) {
    const options = [correctSong.year.toString()];
    const correctYear = correctSong.year;
    
    // Generate 3 random years around the correct year
    while (options.length < 4) {
      const offset = Math.floor(Math.random() * 10) - 5; // -5 to +5 years
      const wrongYear = correctYear + offset;
      if (wrongYear !== correctYear && !options.includes(wrongYear.toString())) {
        options.push(wrongYear.toString());
      }
    }

    return options;
  }

  // Shuffle array
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Export for use in other files
window.MusicLoader = MusicLoader;
