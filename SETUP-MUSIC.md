# 🎵 MorriQuiz Muziek Setup - Echte Build

## ⚠️ Belangrijke Wijziging

De demo data is verwijderd! MorriQuiz leest nu **alleen echte MP3 bestanden** van je hardeschijf.

## 📁 Muziek Bestanden Toevoegen

### 1. Bestandsnaam Formaat (VERPLICHT):
```
Artiest - Titel - Jaar.mp3
```

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON> van Correcte Bestandsnamen:
```
The Beatles - Hey Jude - 1968.mp3
Queen - Bohemian Rhapsody - 1975.mp3
<PERSON> - <PERSON> - 1983.mp3
Nirvana - Smells Like Teen Spirit - 1991.mp3
Eminem - Lose Yourself - 2002.mp3
Adele - Rolling in the Deep - 2010.mp3
The Weeknd - Blinding Lights - 2020.mp3
```

### 3. Mappen Structuur:
```
MorriQuiz/
└── music/
    ├── 60s/    (1960-1969)
    ├── 70s/    (1970-1979)
    ├── 80s/    (1980-1989)
    ├── 90s/    (1990-1999)
    ├── 00s/    (2000-2009)
    ├── 10s/    (2010-2019)
    └── 20s/    (2020-2029)
```

## 🚀 Snelle Start

### Stap 1: Muziek Toevoegen
1. Ga naar de `music/` map
2. Kies de juiste decade map (bijv. `80s` voor jaren 80 muziek)
3. Plaats je MP3 bestanden met het correcte formaat
4. **Minimaal 10 bestanden per decade** voor beste ervaring

### Stap 2: Server Starten
```bash
# Dubbelklik op een van deze:
start-server.bat
start-server.ps1

# Of handmatig:
python music-server.py
```

### Stap 3: Website Openen
- Lokaal: `http://localhost:8080`
- Tailscale: `http://*************:8080`

## 🔧 Nieuwe Features

### Automatische Bestand Detectie:
- Server scant automatisch je music mappen
- API endpoint: `/api/music/[decade]`
- Real-time bestand detectie

### Verbeterde Foutmeldingen:
- Duidelijke meldingen als bestanden ontbreken
- Instructies voor correcte bestandsnamen
- Debugging informatie in browser console

## ❌ Wat Niet Meer Werkt

- **Demo data**: Volledig verwijderd
- **Placeholder muziek**: Niet meer beschikbaar
- **Quiz zonder bestanden**: Geeft nu foutmelding

## ✅ Wat Je Moet Doen

1. **Voeg echte MP3 bestanden toe** aan de music mappen
2. **Gebruik het correcte bestandsnaam formaat**
3. **Start de nieuwe music-server.py** in plaats van de basis HTTP server

## 🎯 Voorbeeld Setup

Voor een werkende 80s quiz:
```
music/80s/
├── Michael Jackson - Billie Jean - 1983.mp3
├── Madonna - Like a Virgin - 1984.mp3
├── Prince - Purple Rain - 1984.mp3
├── Duran Duran - Hungry Like the Wolf - 1982.mp3
├── The Police - Every Breath You Take - 1983.mp3
├── Bon Jovi - Livin on a Prayer - 1986.mp3
├── Whitney Houston - I Wanna Dance with Somebody - 1987.mp3
├── Guns N Roses - Sweet Child O Mine - 1988.mp3
├── Cyndi Lauper - Time After Time - 1984.mp3
└── Depeche Mode - Personal Jesus - 1989.mp3
```

## 🐛 Troubleshooting

### "Geen muziekbestanden gevonden":
1. Controleer of bestanden in de juiste map staan
2. Controleer bestandsnaam formaat: `Artiest - Titel - Jaar.mp3`
3. Zorg voor minimaal 1 bestand per decade

### Server start niet:
1. Controleer of Python is geïnstalleerd: `python --version`
2. Probeer: `py music-server.py`
3. Controleer of poort 8080 vrij is

### Audio speelt niet:
1. Controleer of bestanden daadwerkelijk MP3 formaat hebben
2. Test bestand in een media player
3. Controleer browser console voor fouten

## 📊 Server Informatie

De nieuwe `music-server.py` biedt:
- **Directory scanning** voor automatische bestand detectie
- **JSON API** voor muziek bestanden
- **Betere foutafhandeling**
- **CORS ondersteuning** voor lokale ontwikkeling
- **Mooie directory listings** met MP3 iconen

---

**Nu ben je klaar voor de echte MorriQuiz ervaring met je eigen muziek! 🎵🎉**
