#!/usr/bin/env python3
"""
MorriQuiz Correct Years Fixer
Fixes the years in filenames to match the actual release years of the songs
"""

import os
import re
from pathlib import Path

# Database of correct release years for songs
SONG_YEARS = {
    # 10s decade songs with correct years
    "AF<PERSON><PERSON><PERSON><PERSON>, WR<PERSON>EL - Ten Feet Tall": 2014,
    "<PERSON> - <PERSON> (<PERSON><PERSON> Perfect's \"When I'm Gone\")": 2013,
    "Becky G - Shower": 2014,
    "<PERSON>, <PERSON>": 2014,
    "Clean Bandit, <PERSON> - <PERSON>": 2014,
    "Coldplay - A Sky Full of Stars": 2014,
    "Dotan - Home": 2014,
    "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>": 2014,
    "Galantis - Runaway": 2014,
    "<PERSON> - <PERSON>": 2014,
    "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> - <PERSON>": 2014,
    "Kensington - War": 2014,
    "<PERSON><PERSON><PERSON>, <PERSON> - <PERSON>": 2014,
    "<PERSON> and <PERSON>, <PERSON> - Prayer in C": 2014,
    "<PERSON>, <PERSON> - Uptown Funk": 2014,
    "Mar<PERSON> 5 - Sugar": 2014,
    "<PERSON><PERSON> - All About That Bass": 2014,
    "<PERSON><PERSON>": 2014,
    "<PERSON><PERSON>, <PERSON>": 2014,
    "<PERSON><PERSON>, <PERSON> - <PERSON>ball": 2014,
    "<PERSON><PERSON>, <PERSON>e-Yo - Time of Our Lives": 2014,
    "<PERSON> <PERSON> - <PERSON> I Can": 2014,
    "Script - Superheroes": 2014,
    "Sheppard - Geronimo": 2014,
    "Tove Lo, Hippie Sabotage - Stay High": 2014,
    "WAL<PERSON> THE MOON - Shut Up and Dance": 2014,
    "Adam Lambert - Ghost Town": 2015,
    "Addicted To You - Avicii": 2013,  # Note: This should be "Avicii - Addicted To You"
    "Alvaro Soler - Sofia": 2016,
    "Amy Macdonald - This Is The Life": 2007,  # Actually from 00s
    "Anders Nilsen - Salsa Tequila": 2000,  # Actually from 00s
    "Anna Kendrick - Cups": 2013,
    "Armin van Buuren, Mr. Probz - Another You": 2015,
    "Avicii - Hey Brother": 2013,
    "Becky G - Shower": 2014,
    "Bruno Mars - Grenade": 2010,
    "Calvin Harris - My Way": 2016,
    "Calvin Harris, John Newman - Blame": 2014,
    "Calvin Harris, Rihanna - This Is What You Came For": 2016,
    "Clean Bandit, Jess Glynne - Rather Be": 2014,
    "Coldplay - A Sky Full of Stars": 2014,
    "DNCE - Cake By The Ocean": 2015,
    "David Guetta, AFROJACK, Bebe Rexha, Nicki Minaj - Hey Mama": 2015,
    "David Guetta, USHER - Without You": 2011,
    "Dotan - Home": 2014,
    "Eminem, Rihanna - The Monster": 2013,
    "Enrique Iglesias, Sean Paul, Descemer Bueno, Gente De Zona - Bailando": 2014,
    "Enrique Iglesias, Wisin - DUELE EL CORAZON": 2016,
    "Faul & Wad, PNAU - Changes": 2013,
    "Felix Jaehn, Jasmine Thompson - Ain't Nobody": 2015,
    "Flo Rida, Robin Thicke, Verdine White - I Don't Like It, I Love It": 2015,
    "FÄIS, AFROJACK - Hey": 2017,
    "FÄIS, AFROJACK - Used To Have It All": 2018,
    "GIMS - J'me tire": 2013,
    "Galantis - No Money": 2016,
    "Galantis - Runaway": 2014,
    "George Ezra - Budapest": 2014,
    "Gotye - Somebody That I Used To Know": 2011,
    "Hozier - Take Me To Church": 2013,
    "Imagine Dragons - Demons": 2012,
    "Jason Derulo - Want to Want Me": 2015,
    "Jess Glynne - Hold My Hand": 2015,
    "Jessie J, Ariana Grande, Nicki Minaj - Bang Bang": 2014,
    "Jessie J, B.o.B - Price Tag": 2011,
    "John Legend - All of Me": 2013,
    "Jonas Blue, Dakota - Fast Car": 2016,
    "Justin Bieber - Sorry": 2015,
    "Justin Bieber - What Do You Mean": 2015,
    "Katy Perry - Last Friday Night": 2011,
    "Katy Perry, Juicy J - Dark Horse": 2013,
    "Kensington - War": 2014,
    "Klingande - Jubel": 2013,
    "Kungs, Cookin' On 3 Burners - This Girl": 2016,
    "Kygo, Conrad Sewell - Firestone": 2014,
    "Kygo, Parson James - Stole the Show": 2015,
    "Lilly Wood and The Prick, Robin Schulz - Prayer in C": 2014,
    "Lost Frequencies, Janieck - Reality": 2015,
    "LunchMoney Lewis - Bills": 2015,
    "Lykke Li, The Magician - I Follow Rivers": 2012,
    "MAGIC! - Rude": 2013,
    "Major Lazer, Nyla, Fuse ODG - Light It Up": 2015,
    "Mark Ronson, Bruno Mars - Uptown Funk": 2014,
    "Maroon 5 - Sugar": 2014,
    "Martin Solveig, GTA - Intoxicated": 2015,
    "Meghan Trainor - All About That Bass": 2014,
    "Meghan Trainor - Lips Are Movin": 2014,
    "Milky Chance - Stolen Dance": 2013,
    "Mr. Probz - Nothing Really Matters": 2013,
    "Mr. Probz, Robin Schulz - Waves": 2014,
    "Naughty Boy, Sam Smith - La La La": 2013,
    "Nicky Jam, Enrique Iglesias - El Perdón": 2015,
    "Nico & Vinz - Am I Wrong": 2013,
    "OMI, Felix Jaehn - Cheerleader": 2015,
    "Of Monsters and Men - Little Talks": 2011,
    "Olly Alexander (Years & Years) - King": 2015,
    "OneRepublic - Counting Stars": 2013,
    "Pharrell Williams - Freedom": 2015,
    "Pitbull, John Ryan - Fireball": 2014,
    "Pitbull, Kesha - Timber": 2013,
    "Pitbull, Ne-Yo - Time of Our Lives": 2014,
    "R. City, Adam Levine - Locked Away": 2015,
    "Racoon - No Mercy": 2013,
    "Rihanna - Diamonds": 2012,
    "Robbie Williams - Candy": 2012,
    "Sam Feldt, Lucas & Steve, Wulf - Summer On You": 2016,
    "Sam Smith - Like I Can": 2014,
    "Script - Superheroes": 2014,
    "Shawn Mendes - Stitches": 2015,
    "Sheppard - Geronimo": 2014,
    "Stromae - Formidable": 2013,
    "Tove Lo, Hippie Sabotage - Stay High": 2014,
    "Train - Drive By": 2012,
    "Vance Joy - Riptide": 2013,
    "WALK THE MOON - Shut Up and Dance": 2014,
    "Willy William - Ego": 2015,
    "Wiz Khalifa, Charlie Puth - See You Again": 2015,
}

def extract_artist_title(filename):
    """Extract artist and title from filename."""
    name = filename.replace('.mp3', '')
    parts = name.split(' - ')
    
    if len(parts) >= 2:
        artist = parts[0].strip()
        title = parts[1].strip()
        return f"{artist} - {title}"
    
    return name

def fix_year_in_filename(mp3_file):
    """Fix the year in a single filename."""
    original_name = mp3_file.name
    artist_title = extract_artist_title(original_name)
    
    # Look up correct year
    correct_year = SONG_YEARS.get(artist_title)
    
    if correct_year is None:
        print(f"⚠️  No year data for: {artist_title}")
        return False
    
    # Create new filename with correct year
    new_name = f"{artist_title} - {correct_year}.mp3"
    
    if new_name == original_name:
        return False  # No change needed
    
    new_path = mp3_file.parent / new_name
    
    # Check if new filename already exists
    counter = 1
    while new_path.exists():
        base_name = f"{artist_title} - {correct_year}"
        new_name = f"{base_name} ({counter}).mp3"
        new_path = mp3_file.parent / new_name
        counter += 1
    
    try:
        mp3_file.rename(new_path)
        print(f"✓ Fixed: {original_name}")
        print(f"     To: {new_name}")
        return True
    except Exception as e:
        print(f"✗ Error fixing {original_name}: {e}")
        return False

def fix_years_in_directory(decade_dir):
    """Fix years in all MP3 files in a decade directory."""
    decade = decade_dir.name
    mp3_files = list(decade_dir.glob("*.mp3"))
    
    if not mp3_files:
        print(f"No MP3 files found in {decade_dir}")
        return
    
    print(f"\n=== Fixing years in {decade} ===")
    print(f"Found {len(mp3_files)} MP3 files")
    
    fixed_count = 0
    
    for mp3_file in mp3_files:
        if fix_year_in_filename(mp3_file):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files in {decade}")

def main():
    """Main function to fix years in all music directories."""
    music_dir = Path("music")
    
    if not music_dir.exists():
        print("Error: music/ directory not found!")
        return
    
    print("🎵 MorriQuiz Correct Years Fixer")
    print("=" * 50)
    print("Fixing years to match actual release dates")
    
    # Process each decade directory
    for decade in ['60s', '70s', '80s', '90s', '00s', '10s', '20s']:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            fix_years_in_directory(decade_dir)
        else:
            print(f"Directory {decade} not found, skipping...")
    
    print("\n" + "=" * 50)
    print("✅ Year correction complete!")
    print("All files now have correct release years")

if __name__ == "__main__":
    main()
