// MorriQuiz - Muziek Quiz JavaScript

// Global variables
let currentPlayer = null;
let currentDecade = null;
let gameData = {
  players: {},
  leaderboard: [],
  lastUpdated: new Date().toISOString()
};

// Account System Functions
function showLoginForm() {
  document.getElementById('login-form').classList.remove('hidden');
  document.getElementById('register-form').classList.add('hidden');
  clearAuthError();
}

function showRegisterForm() {
  document.getElementById('login-form').classList.add('hidden');
  document.getElementById('register-form').classList.remove('hidden');
  clearAuthError();
}

function clearAuthError() {
  const errorDiv = document.getElementById('auth-error');
  if (errorDiv) {
    errorDiv.classList.add('hidden');
    errorDiv.textContent = '';
  }
}

function showAuthError(message) {
  const errorDiv = document.getElementById('auth-error');
  if (errorDiv) {
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
  }
}

async function loginUser() {
  // Check if AccountManager is loaded
  if (!window.AccountManager) {
    showAuthError('Account systeem wordt geladen... Probeer het opnieuw.');
    return;
  }

  const username = document.getElementById('login-username').value.trim();
  const password = document.getElementById('login-password').value;

  if (!username || !password) {
    showAuthError('Vul alle velden in');
    return;
  }

  try {
    showAuthError('Inloggen...'); // Show loading message
    const user = await window.AccountManager.login(username, password);
    currentPlayer = user.username;
    showUserDashboard(user);
    clearAuthError();
  } catch (error) {
    showAuthError(error.message);
  }
}

async function registerUser() {
  // Check if AccountManager is loaded
  if (!window.AccountManager) {
    showAuthError('Account systeem wordt geladen... Probeer het opnieuw.');
    return;
  }

  const username = document.getElementById('register-username').value.trim();
  const password = document.getElementById('register-password').value;
  const confirmPassword = document.getElementById('register-confirm').value;

  if (!username || !password || !confirmPassword) {
    showAuthError('Vul alle velden in');
    return;
  }

  if (password !== confirmPassword) {
    showAuthError('Wachtwoorden komen niet overeen');
    return;
  }

  try {
    showAuthError('Account aanmaken...'); // Show loading message
    const user = await window.AccountManager.createAccount(username, password);
    currentPlayer = user.username;
    showUserDashboard(user);
    clearAuthError();
    window.AccountManager.showNotification('Account succesvol aangemaakt! 🎉', 'success');
  } catch (error) {
    showAuthError(error.message);
  }
}

function showUserDashboard(user) {
  // Hide auth forms
  document.getElementById('login-form').classList.add('hidden');
  document.getElementById('register-form').classList.add('hidden');

  // Show user dashboard
  document.getElementById('user-dashboard').classList.remove('hidden');

  // Update user info
  document.getElementById('current-username').textContent = user.username;
  document.getElementById('user-level').textContent = user.level || 1;
  document.getElementById('user-total-score').textContent = user.totalScore || 0;
  document.getElementById('user-games-played').textContent = user.gamesPlayed || 0;

  // Update XP progress
  const levelInfo = window.AccountManager.getUserLevelInfo(user);
  if (levelInfo) {
    const progressBar = document.getElementById('xp-progress');
    const xpText = document.getElementById('xp-text');

    if (progressBar && xpText) {
      progressBar.style.width = `${levelInfo.progressPercentage}%`;
      xpText.textContent = `${levelInfo.xpProgress} / 1000 XP`;
    }
  }
}

function logoutUser() {
  // Check if AccountManager is loaded
  if (window.AccountManager) {
    window.AccountManager.logout();
  }

  currentPlayer = null;

  // Hide user dashboard
  document.getElementById('user-dashboard').classList.add('hidden');

  // Show login form
  document.getElementById('login-form').classList.remove('hidden');

  // Clear form fields
  document.getElementById('login-username').value = '';
  document.getElementById('login-password').value = '';
  document.getElementById('register-username').value = '';
  document.getElementById('register-password').value = '';
  document.getElementById('register-confirm').value = '';

  clearAuthError();
}

// Initialize account system when ready
function initializeAccountSystem() {
  // Hide loading indicator
  const accountLoading = document.getElementById('account-loading');
  if (accountLoading) {
    accountLoading.classList.add('hidden');
  }

  // Check if user is already logged in
  if (window.AccountManager && window.AccountManager.loadSession()) {
    const user = window.AccountManager.getCurrentUser();
    if (user) {
      currentPlayer = user.username;
      showUserDashboard(user);
      return;
    }
  }

  // Show login form if not logged in
  const loginForm = document.getElementById('login-form');
  if (loginForm) {
    loginForm.classList.remove('hidden');
  }

  // Auto-focus on username input
  setTimeout(() => {
    const usernameInput = document.getElementById('login-username');
    if (usernameInput) {
      usernameInput.focus();
    }
  }, 100);
}

// Wait for AccountManager to be available
let accountManagerRetries = 0;
const maxRetries = 50; // 5 seconds max wait time

function waitForAccountManager() {
  if (window.AccountManager) {
    initializeAccountSystem();
  } else if (accountManagerRetries < maxRetries) {
    accountManagerRetries++;

    // Show loading indicator
    const accountLoading = document.getElementById('account-loading');
    if (accountLoading) {
      accountLoading.classList.remove('hidden');
    }

    // Hide auth forms while loading
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    if (loginForm) loginForm.classList.add('hidden');
    if (registerForm) registerForm.classList.add('hidden');

    // Retry after a short delay
    setTimeout(waitForAccountManager, 100);
  } else {
    // Fallback: Show error message and basic login form
    showAccountSystemError();
  }
}

function showAccountSystemError() {
  // Hide loading indicator
  const accountLoading = document.getElementById('account-loading');
  if (accountLoading) {
    accountLoading.classList.add('hidden');
  }

  // Show error message
  showAuthError('Account systeem kon niet geladen worden. Probeer de pagina te verversen.');

  // Show login form anyway (will show error when trying to use)
  const loginForm = document.getElementById('login-form');
  if (loginForm) {
    loginForm.classList.remove('hidden');
  }
}

// Listen for AccountManager ready event
window.addEventListener('accountManagerReady', function() {
  console.log('📢 AccountManager ready event received');
  initializeAccountSystem();
});

// Hide loader when page loads
window.addEventListener("load", function () {
  const loader = document.getElementById("loader");
  loader.style.display = "none";
  loadGameData();

  // Wait for account system to be ready
  waitForAccountManager();
});

// Load game data from localStorage
function loadGameData() {
  const savedData = localStorage.getItem('morriQuizData');
  if (savedData) {
    gameData = JSON.parse(savedData);
  }
}

// Save game data to localStorage
function saveGameData() {
  localStorage.setItem('morriQuizData', JSON.stringify(gameData));
}

// Start quiz function (now for logged in users)
function startQuiz() {
  // Check if user is logged in
  if (!window.AccountManager || !window.AccountManager.isLoggedIn()) {
    showAuthError('Je moet ingelogd zijn om te spelen');
    return;
  }

  const user = window.AccountManager.getCurrentUser();
  if (!user) {
    showAuthError('Geen geldige gebruiker gevonden');
    return;
  }

  currentPlayer = user.username;

  // Initialize legacy player data for backwards compatibility
  if (!gameData.players[currentPlayer]) {
    gameData.players[currentPlayer] = {
      name: currentPlayer,
      totalScore: user.totalScore || 0,
      gamesPlayed: user.gamesPlayed || 0,
      bestScore: user.bestScore || 0,
      bestDecade: user.bestDecade || null,
      totalPlayTime: user.totalPlayTime || 0,
      decadeScores: user.decadeScores || {},
      lastPlayed: new Date().toISOString(),
      createdAt: user.createdAt || new Date().toISOString()
    };
  }

  saveGameData();
  showModeSelection();
}

// Show mode selection screen (now goes directly to decade selection)
function showModeSelection() {
  // Hide auth screens
  document.getElementById('login-form').classList.add('hidden');
  document.getElementById('register-form').classList.add('hidden');
  document.getElementById('user-dashboard').classList.add('hidden');

  // Show mode selection
  document.getElementById('mode-screen').classList.remove('hidden');

  const welcomeMessage = document.getElementById('welcome-message');
  if (welcomeMessage) {
    welcomeMessage.textContent = `Welkom, ${currentPlayer}!`;
  }
}

// Start singleplayer mode - go to decade selection
function startSingleplayer() {
  document.getElementById('mode-screen').classList.add('hidden');
  document.getElementById('decade-screen').classList.remove('hidden');

  const welcomeMessage = document.getElementById('decade-welcome-message');
  welcomeMessage.textContent = `Kies een decade, ${currentPlayer}!`;

  // Show active sessions if any
  showActiveSessions();

  // Update and show leaderboard
  updateLeaderboard();
}

// Show decade selection (used by other functions)
function showDecadeSelection() {
  document.getElementById('name-screen').classList.add('hidden');
  document.getElementById('decade-screen').classList.remove('hidden');

  const welcomeMessage = document.getElementById('welcome-message');
  welcomeMessage.textContent = `Welkom, ${currentPlayer}!`;

  // Show active sessions if any
  showActiveSessions();

  // Update and show leaderboard
  updateLeaderboard();
}

// Back to mode selection
function backToModeSelection() {
  document.getElementById('decade-screen').classList.add('hidden');
  document.getElementById('mode-screen').classList.remove('hidden');
}

// Back to auth screen (login/register)
function backToAuthScreen() {
  document.getElementById('mode-screen').classList.add('hidden');
  document.getElementById('decade-screen').classList.add('hidden');

  // Show user dashboard if logged in, otherwise show login
  if (window.AccountManager && window.AccountManager.isLoggedIn()) {
    document.getElementById('user-dashboard').classList.remove('hidden');
  } else {
    document.getElementById('login-form').classList.remove('hidden');
  }
}

// Multiplayer functions removed

// Back to decade selection (for singleplayer)
function backToDecadeSelection() {
  document.getElementById('decade-screen').classList.add('hidden');
  document.getElementById('mode-screen').classList.remove('hidden');
}

// Show active quiz sessions for current player
function showActiveSessions() {
  const player = gameData.players[currentPlayer];
  if (!player) return;

  const decadeButtons = document.querySelectorAll('.decade-btn');

  // Show active sessions
  if (player.activeSessions) {
    Object.keys(player.activeSessions).forEach(decade => {
      const session = player.activeSessions[decade];
      if (!session.isCompleted) {
        // Find the decade button and add progress indicator
        decadeButtons.forEach(btn => {
          if (btn.onclick.toString().includes(`'${decade}'`)) {
            const progress = Math.round((session.currentQuestion / 100) * 100);
            const progressText = document.createElement('div');
            progressText.className = 'progress-indicator';
            progressText.innerHTML = `<small>Voortgang: ${session.currentQuestion}/100 (${progress}%)</small>`;
            progressText.style.cssText = 'font-size: 0.8rem; color: #fff; margin-top: 5px; opacity: 0.9;';
            btn.appendChild(progressText);
          }
        });
      }
    });
  }

  // Show decade statistics
  if (player.decadeScores) {
    decadeButtons.forEach(btn => {
      const decade = btn.onclick.toString().match(/'([^']+)'/)?.[1];
      if (decade && player.decadeScores[decade]) {
        const stats = player.decadeScores[decade];
        const avgScore = stats.gamesPlayed > 0 ? Math.round(stats.totalScore / stats.gamesPlayed) : 0;

        const statsText = document.createElement('div');
        statsText.className = 'decade-stats';
        statsText.innerHTML = `<small>Best: ${stats.bestScore} | Ø ${avgScore} | ${stats.gamesPlayed}x</small>`;
        statsText.style.cssText = 'font-size: 0.7rem; color: rgba(255,255,255,0.8); margin-top: 3px;';
        btn.appendChild(statsText);
      }
    });
  }
}

// This function is now handled by backToAuthScreen above

// Select decade and start quiz
function selectDecade(decade) {
  currentDecade = decade;
  
  // Check if music files exist for this decade
  checkMusicFiles(decade).then(hasMusic => {
    if (hasMusic) {
      // Redirect to quiz page
      window.location.href = `quiz/quiz.html?decade=${decade}&player=${encodeURIComponent(currentPlayer)}`;
    } else {
      alert(`Geen muziekbestanden gevonden voor de ${decade}. Zorg ervoor dat je MP3 bestanden in de map 'music/${decade}' hebt geplaatst.`);
    }
  });
}

// Check if music files exist for decade
async function checkMusicFiles(decade) {
  try {
    // Try to fetch a test file to see if the directory exists
    const response = await fetch(`music/${decade}/test.mp3`);
    return response.ok || response.status === 404; // 404 means directory exists but file doesn't
  } catch (error) {
    // For local development, we'll assume files exist
    console.warn(`Kan muziekbestanden voor ${decade} niet controleren:`, error);
    return true; // Assume files exist for development
  }
}

// Display leaderboard
function displayLeaderboard() {
  const leaderboardList = document.getElementById('leaderboard-list');
  
  if (!leaderboardList) return;
  
  // Create leaderboard from player data
  const leaderboard = Object.values(gameData.players)
    .filter(player => player.totalScore > 0)
    .sort((a, b) => b.totalScore - a.totalScore)
    .slice(0, 10); // Top 10
  
  if (leaderboard.length === 0) {
    leaderboardList.innerHTML = '<p style="color: #666; font-style: italic;">Nog geen scores beschikbaar</p>';
    return;
  }
  
  leaderboardList.innerHTML = '';
  
  leaderboard.forEach((player, index) => {
    const item = document.createElement('div');
    item.className = 'leaderboard-item';
    
    const rank = index + 1;
    let rankEmoji = '';
    if (rank === 1) rankEmoji = '🥇';
    else if (rank === 2) rankEmoji = '🥈';
    else if (rank === 3) rankEmoji = '🥉';
    else rankEmoji = `${rank}.`;
    
    item.innerHTML = `
      <span class="leaderboard-rank">${rankEmoji}</span>
      <span class="leaderboard-name">${player.name}</span>
      <span class="leaderboard-score">${player.totalScore} pts</span>
    `;
    
    leaderboardList.appendChild(item);
  });
}

// Update player score
async function updatePlayerScore(score, decade, playTime = 0, correctAnswers = 0, totalQuestions = 100) {
  // Use account system if available
  if (window.AccountManager && window.AccountManager.isLoggedIn()) {
    try {
      await window.AccountManager.updateUserStats(score, decade, playTime, correctAnswers, totalQuestions);
      updateLeaderboard();
      return;
    } catch (error) {
      console.error('Failed to update stats:', error);
      // Continue with fallback system
    }
  }

  // Fallback to old system for backwards compatibility
  if (!currentPlayer || !gameData.players[currentPlayer]) return;

  const player = gameData.players[currentPlayer];
  player.totalScore += score;
  player.gamesPlayed += 1;
  player.lastPlayed = new Date().toISOString();
  player.totalPlayTime = (player.totalPlayTime || 0) + playTime;

  // Initialize decade scores if not exists
  if (!player.decadeScores) {
    player.decadeScores = {};
  }

  // Initialize decade-specific stats
  if (!player.decadeScores[decade]) {
    player.decadeScores[decade] = {
      totalScore: 0,
      gamesPlayed: 0,
      bestScore: 0,
      totalPlayTime: 0,
      totalCorrectAnswers: 0,
      totalQuestions: 0,
      games: []
    };
  }

  const decadeStats = player.decadeScores[decade];
  decadeStats.totalScore += score;
  decadeStats.gamesPlayed += 1;
  decadeStats.totalPlayTime += playTime;
  decadeStats.totalCorrectAnswers += correctAnswers;
  decadeStats.totalQuestions += totalQuestions;

  // Add this game to the history
  decadeStats.games.push({
    score: score,
    correctAnswers: correctAnswers,
    totalQuestions: totalQuestions,
    playTime: playTime,
    date: new Date().toISOString()
  });

  // Update best score for this decade
  if (score > decadeStats.bestScore) {
    decadeStats.bestScore = score;
  }

  // Update overall best score if this is better
  if (score > (player.bestScore || 0)) {
    player.bestScore = score;
    player.bestDecade = decade;
  }

  saveGameData();
  updateLeaderboard();
}

// Update leaderboard
async function updateLeaderboard() {
  // Use account system leaderboard if available
  if (window.AccountManager) {
    try {
      const accountLeaderboard = await window.AccountManager.getLeaderboard();
      const leaderboardElement = document.getElementById('leaderboard');
      if (leaderboardElement) {
        displayAccountLeaderboard(accountLeaderboard);
      }
      return;
    } catch (error) {
      console.error('Failed to get leaderboard:', error);
      // Continue with fallback
    }
  }

  // Fallback to old system
  const players = Object.values(gameData.players);

  // Sort players by total score (descending)
  players.sort((a, b) => (b.totalScore || 0) - (a.totalScore || 0));

  // Update leaderboard display if element exists
  const leaderboardElement = document.getElementById('leaderboard');
  if (leaderboardElement) {
    displayLeaderboard(players);
  }
}

// Display account-based leaderboard
function displayAccountLeaderboard(leaderboard) {
  const leaderboardElement = document.getElementById('leaderboard');
  if (!leaderboardElement) return;

  if (!leaderboard || leaderboard.length === 0) {
    leaderboardElement.innerHTML = '<p>Nog geen spelers in het leaderboard.</p>';
    return;
  }

  let html = '<h3>🏆 Leaderboard</h3><div class="leaderboard-list">';

  leaderboard.forEach((player, index) => {
    const averageScore = player.gamesPlayed > 0 ? Math.round(player.totalScore / player.gamesPlayed) : 0;
    const bestDecade = player.bestDecade || 'Geen';

    html += `
      <div class="leaderboard-item ${index === 0 ? 'first-place' : ''} ${player.isCurrentUser ? 'current-user' : ''}">
        <div class="rank">${player.rank}</div>
        <div class="player-info">
          <div class="player-name">
            ${player.username}
            ${player.isCurrentUser ? '<span class="you-badge">Jij</span>' : ''}
            <span class="level-badge">Lv.${player.level}</span>
          </div>
          <div class="player-stats">
            <span class="total-score">${player.totalScore} punten</span>
            <span class="games-played">${player.gamesPlayed} games</span>
          </div>
          <div class="player-details">
            <span class="average">Ø ${averageScore} punten</span>
            <span class="best-score">Best: ${player.bestScore} (${bestDecade})</span>
          </div>
        </div>
      </div>
    `;
  });

  html += '</div>';
  leaderboardElement.innerHTML = html;
}

// Display leaderboard (fallback for old system)
function displayLeaderboard(players) {
  const leaderboardElement = document.getElementById('leaderboard');
  if (!leaderboardElement) return;

  if (!players || players.length === 0) {
    leaderboardElement.innerHTML = '<p>Nog geen spelers in het leaderboard.</p>';
    return;
  }

  let html = '<h3>🏆 Leaderboard</h3><div class="leaderboard-list">';

  players.slice(0, 10).forEach((player, index) => {
    const totalPlayTime = formatPlayTime(player.totalPlayTime || 0);
    const averageScore = player.gamesPlayed > 0 ? Math.round(player.totalScore / player.gamesPlayed) : 0;
    const bestDecade = player.bestDecade || 'Geen';

    // Count decades played
    const decadesPlayed = Object.keys(player.decadeScores || {}).length;

    html += `
      <div class="leaderboard-item ${index === 0 ? 'first-place' : ''}">
        <div class="rank">${index + 1}</div>
        <div class="player-info">
          <div class="player-name">${player.name}</div>
          <div class="player-stats">
            <span class="total-score">${player.totalScore || 0} punten</span>
            <span class="games-played">${player.gamesPlayed || 0} games</span>
            <span class="play-time">⏱️ ${totalPlayTime}</span>
          </div>
          <div class="player-details">
            <span class="average">Ø ${averageScore} punten</span>
            <span class="best-score">Best: ${player.bestScore || 0} (${bestDecade})</span>
            <span class="decades">${decadesPlayed} decades</span>
          </div>
        </div>
      </div>
    `;
  });

  html += '</div>';
  leaderboardElement.innerHTML = html;
}

// Format play time in readable format
function formatPlayTime(seconds) {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
}

// Get player statistics
function getPlayerStats(playerName) {
  const player = gameData.players[playerName];
  if (!player) return null;

  return {
    name: player.name,
    totalScore: player.totalScore,
    gamesPlayed: player.gamesPlayed,
    totalPlayTime: player.totalPlayTime || 0,
    averageScore: player.gamesPlayed > 0 ? Math.round(player.totalScore / player.gamesPlayed) : 0,
    bestScore: player.bestScore || 0,
    bestDecade: player.bestDecade,
    decadeScores: player.decadeScores,
    lastPlayed: player.lastPlayed
  };
}

// Multiplayer functions removed for simplicity

// Export functions for use in other files
window.MorriQuiz = {
  updatePlayerScore,
  getPlayerStats,
  saveGameData,
  loadGameData,
  gameData
};
