<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MorriQuiz De<PERSON> - <PERSON><PERSON><PERSON> Quiz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .demo-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: left;
        }
        
        .demo-info h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .demo-info ul {
            line-height: 1.8;
        }
        
        .demo-info li {
            margin: 10px 0;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            color: #856404;
        }
        
        .warning h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .feature h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .steps {
            text-align: left;
            background: rgba(255, 255, 255, 0.7);
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
        }
        
        .steps h3 {
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .steps ol {
            line-height: 1.8;
        }
        
        .steps li {
            margin: 15px 0;
            padding-left: 10px;
        }
        
        .code {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-logo">
            <img src="logo.png" alt="MorriQuiz Logo" style="max-width: 150px; max-height: 150px; margin-bottom: 20px; filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));">
        </div>
        <h1>Demo</h1>
        <p style="font-size: 1.2rem; color: #666; margin-bottom: 30px;">
            Welkom bij de MorriQuiz muziek quiz website!
        </p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎵</div>
                <h4>7 Decennia</h4>
                <p>Van de jaren 60 tot de jaren 20</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💯</div>
                <h4>100 Vragen</h4>
                <p>Uitgebreide quiz per decade</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <h4>Auto-Opslag</h4>
                <p>Pauzeer en hervat op elk moment</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🏆</div>
                <h4>Leaderboard</h4>
                <p>Vergelijk je scores</p>
            </div>
        </div>
        
        <div class="warning">
            <h4>⚠️ Belangrijk - Echte Build</h4>
            <p><strong>Demo data is verwijderd!</strong> Je moet nu echte MP3 bestanden toevoegen aan de music/ mappen om de quiz te kunnen spelen. Gebruik het formaat: <code>Artiest - Titel - Jaar.mp3</code></p>
        </div>
        
        <div class="demo-info">
            <h3>🎮 Hoe werkt het?</h3>
            <ul>
                <li><strong>Naam invoeren:</strong> Voer je naam in op het startscherm</li>
                <li><strong>Decade kiezen:</strong> Selecteer een muziekdecade</li>
                <li><strong>Muziek beluisteren:</strong> Luister naar het fragment</li>
                <li><strong>Vragen beantwoorden:</strong> Kies uit 4 antwoordopties</li>
                <li><strong>Score behalen:</strong> 2 punten per correct antwoord</li>
            </ul>
        </div>
        
        <div class="steps">
            <h3>📁 Muziek Toevoegen (Voor Volledige Versie)</h3>
            <ol>
                <li>Ga naar de <code>music/</code> map in je MorriQuiz installatie</li>
                <li>Kies de juiste decade map (60s, 70s, 80s, 90s, 00s, 10s, 20s)</li>
                <li>Plaats MP3 bestanden met het formaat:</li>
                <div class="code">Artiest - Titel - Jaar.mp3</div>
                <li>Voorbeeld:</li>
                <div class="code">The Beatles - Hey Jude - 1968.mp3</div>
                <li>Minimaal 10 bestanden per decade voor beste ervaring</li>
                <li>Herstart de website en geniet van je eigen muziek!</li>
            </ol>
        </div>
        
        <div class="buttons">
            <a href="SETUP-MUSIC.md" class="btn btn-primary">📖 Muziek Setup Guide</a>
            <a href="index.html" class="btn btn-secondary">🚀 Start Quiz (Vereist MP3s)</a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 2px solid rgba(102, 126, 234, 0.2); color: #666; font-style: italic;">
            <p>💡 <strong>Tip:</strong> Voor de beste ervaring, gebruik een lokale webserver zoals <code>python -m http.server</code></p>
        </div>
    </div>
</body>
</html>
