#!/usr/bin/env python3
"""
MorriQuiz Universal Filename Fixer
Converts ALL music files in ALL decades to the correct format: "Artist - Title - Year.mp3"
"""

import os
import re
from pathlib import Path

# Decade year ranges for automatic year assignment
DECADE_YEARS = {
    '60s': (1960, 1969),
    '70s': (1970, 1979),
    '80s': (1980, 1989),
    '90s': (1990, 1999),
    '00s': (2000, 2009),
    '10s': (2010, 2019),
    '20s': (2020, 2029)
}

def clean_and_parse_filename(filename):
    """Clean and parse filename to extract artist and title."""
    # Remove file extension
    name = filename.replace('.mp3', '')
    
    # Remove common prefixes (track numbers, etc.)
    name = re.sub(r'^\d+\.\s*', '', name)  # Remove "01. " etc.
    name = re.sub(r'^\d+\s*-\s*', '', name)  # Remove "01 - " etc.
    
    # Remove common suffixes and patterns
    patterns_to_remove = [
        r'\s*\(.*?Remaster.*?\)',
        r'\s*\(.*?Remix.*?\)',
        r'\s*\(.*?Radio.*?Edit.*?\)',
        r'\s*\(.*?feat\..*?\)',
        r'\s*\(.*?featuring.*?\)',
        r'\s*\(.*?Live.*?\)',
        r'\s*\(.*?Version.*?\)',
        r'\s*\(.*?Mix.*?\)',
        r'\s*\(From.*?\)',
        r'\s*-\s*Radio Edit$',
        r'\s*-\s*Remix$',
        r'\s*-\s*Remaster$',
    ]
    
    for pattern in patterns_to_remove:
        name = re.sub(pattern, '', name, flags=re.IGNORECASE)
    
    # Clean up extra spaces and dashes
    name = re.sub(r'\s+', ' ', name).strip()
    name = re.sub(r'\s*-\s*$', '', name).strip()
    
    return name

def extract_artist_title(cleaned_name):
    """Extract artist and title from cleaned filename."""
    # Try different separators
    separators = [' - ', ' – ', ' — ', ' feat. ', ' featuring ', ' & ', ', ']
    
    for sep in separators:
        if sep in cleaned_name:
            parts = cleaned_name.split(sep, 1)
            if len(parts) == 2:
                artist = parts[0].strip()
                title = parts[1].strip()
                
                # Clean up artist and title
                artist = re.sub(r'^(The\s+)', '', artist, flags=re.IGNORECASE)
                title = re.sub(r'\s*\([^)]*\)$', '', title)  # Remove trailing parentheses
                
                if artist and title:
                    return artist, title
    
    # If no separator found, try to guess
    words = cleaned_name.split()
    if len(words) >= 2:
        # Assume first word(s) are artist, rest is title
        if len(words) == 2:
            return words[0], words[1]
        elif len(words) >= 3:
            # Try to find a good split point
            for i in range(1, len(words)):
                artist = ' '.join(words[:i])
                title = ' '.join(words[i:])
                if len(artist) > 0 and len(title) > 0:
                    return artist, title
    
    # Last resort: use "Unknown Artist"
    return "Unknown Artist", cleaned_name

def get_default_year(decade):
    """Get a default year for the decade (middle year)."""
    start, end = DECADE_YEARS[decade]
    return start + (end - start) // 2

def fix_filename(mp3_file, decade):
    """Fix a single filename to the correct format."""
    original_name = mp3_file.name
    
    # Check if already in correct format
    if re.match(r'^.+ - .+ - \d{4}\.mp3$', original_name):
        print(f"✓ Already correct: {original_name}")
        return False
    
    # Clean and parse the filename
    cleaned_name = clean_and_parse_filename(original_name)
    artist, title = extract_artist_title(cleaned_name)
    
    # Get default year for this decade
    year = get_default_year(decade)
    
    # Create new filename
    new_name = f"{artist} - {title} - {year}.mp3"
    new_path = mp3_file.parent / new_name
    
    # Check if new filename already exists
    counter = 1
    while new_path.exists():
        new_name = f"{artist} - {title} - {year} ({counter}).mp3"
        new_path = mp3_file.parent / new_name
        counter += 1
    
    try:
        # Rename the file
        mp3_file.rename(new_path)
        print(f"✓ Fixed: {original_name}")
        print(f"     To: {new_name}")
        return True
    except Exception as e:
        print(f"✗ Error fixing {original_name}: {e}")
        return False

def fix_files_in_directory(decade_dir):
    """Fix all MP3 files in a decade directory."""
    decade = decade_dir.name
    mp3_files = list(decade_dir.glob("*.mp3"))
    
    if not mp3_files:
        print(f"No MP3 files found in {decade_dir}")
        return
    
    print(f"\n=== Fixing filenames in {decade} ===")
    print(f"Found {len(mp3_files)} MP3 files")
    
    fixed_count = 0
    
    for mp3_file in mp3_files:
        if fix_filename(mp3_file, decade):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} files in {decade}")

def main():
    """Main function to fix filenames in all music directories."""
    music_dir = Path("music")
    
    if not music_dir.exists():
        print("Error: music/ directory not found!")
        return
    
    print("🎵 MorriQuiz Universal Filename Fixer")
    print("=" * 60)
    print("Converting ALL files to format: Artist - Title - Year.mp3")
    
    total_fixed = 0
    
    # Process each decade directory
    for decade in ['60s', '70s', '80s', '90s', '00s', '10s', '20s']:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            files_before = len(list(decade_dir.glob("*.mp3")))
            fix_files_in_directory(decade_dir)
            files_after = len(list(decade_dir.glob("*.mp3")))
            
            if files_before > 0:
                print(f"Summary for {decade}: {files_before} files processed")
        else:
            print(f"Directory {decade} not found, skipping...")
    
    print("\n" + "=" * 60)
    print("✅ Universal filename fixing complete!")
    print("All files are now in format: Artist - Title - Year.mp3")
    print("\nYou can now play quizzes for all decades!")

if __name__ == "__main__":
    main()
