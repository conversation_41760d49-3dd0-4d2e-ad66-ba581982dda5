#!/usr/bin/env python3
"""
Fix <PERSON> filename specifically
"""

from pathlib import Path

def main():
    music_dir = Path("music/10s")
    
    # Find the <PERSON> file
    for file in music_dir.glob("<PERSON>*"):
        if "2014" in file.name:
            new_name = "<PERSON> - <PERSON> - 2013.mp3"
            new_path = music_dir / new_name
            
            try:
                file.rename(new_path)
                print(f"✓ Renamed: {file.name}")
                print(f"     To: {new_name}")
            except Exception as e:
                print(f"✗ Error: {e}")
            break

if __name__ == "__main__":
    main()
