#!/usr/bin/env python3
"""
Show summary of years in music files
"""

import re
from pathlib import Path
from collections import Counter

def main():
    music_dir = Path("music/10s")
    
    if not music_dir.exists():
        print("10s directory not found!")
        return
    
    mp3_files = list(music_dir.glob("*.mp3"))
    
    print("🎵 MorriQuiz Year Summary for 10s")
    print("=" * 40)
    print(f"Total files: {len(mp3_files)}")
    
    # Extract years from filenames
    years = []
    for file in mp3_files:
        # Look for year pattern at the end
        match = re.search(r' - (\d{4})\.mp3$', file.name)
        if match:
            years.append(int(match.group(1)))
    
    # Count years
    year_counts = Counter(years)
    
    print("\nYear distribution:")
    for year in sorted(year_counts.keys()):
        count = year_counts[year]
        print(f"  {year}: {count} songs")
    
    print(f"\nTotal songs with years: {len(years)}")
    print(f"Songs without year pattern: {len(mp3_files) - len(years)}")
    
    # Show decade range
    if years:
        print(f"Year range: {min(years)} - {max(years)}")
        
        # Check if any songs are outside 2010-2019
        outside_decade = [y for y in years if y < 2010 or y > 2019]
        if outside_decade:
            print(f"\n⚠️  Songs outside 2010s decade: {len(outside_decade)}")
            for year in sorted(set(outside_decade)):
                count = outside_decade.count(year)
                print(f"  {year}: {count} songs")

if __name__ == "__main__":
    main()
