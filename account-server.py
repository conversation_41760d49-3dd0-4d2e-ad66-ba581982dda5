#!/usr/bin/env python3
"""
MorriQuiz Account Server
Simple HTTP server for managing user accounts with file-based storage
"""

import json
import os
import hashlib
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class AccountManager:
    def __init__(self, accounts_file='accounts.json'):
        self.accounts_file = accounts_file
        self.accounts = self.load_accounts()
        self.lock = threading.Lock()
    
    def load_accounts(self):
        """Load accounts from file"""
        if os.path.exists(self.accounts_file):
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading accounts: {e}")
                return {}
        return {}
    
    def save_accounts(self):
        """Save accounts to file"""
        try:
            # Create backup
            if os.path.exists(self.accounts_file):
                backup_file = f"{self.accounts_file}.backup"
                os.rename(self.accounts_file, backup_file)
            
            # Save new data
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, indent=2, ensure_ascii=False)
            
            print(f"Accounts saved to {self.accounts_file}")
            return True
        except IOError as e:
            print(f"Error saving accounts: {e}")
            return False
    
    def hash_password(self, password):
        """Simple password hashing"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_account(self, username, password):
        """Create new account"""
        with self.lock:
            username_lower = username.lower()
            
            if username_lower in self.accounts:
                raise ValueError("Gebruikersnaam bestaat al")
            
            account = {
                'username': username,
                'passwordHash': self.hash_password(password),
                'createdAt': datetime.now().isoformat(),
                'lastLogin': datetime.now().isoformat(),
                'totalScore': 0,
                'gamesPlayed': 0,
                'bestScore': 0,
                'bestDecade': None,
                'totalPlayTime': 0,
                'decadeScores': {},
                'achievements': [],
                'level': 1,
                'experience': 0
            }
            
            self.accounts[username_lower] = account
            self.save_accounts()
            return account
    
    def login(self, username, password):
        """Login user"""
        with self.lock:
            username_lower = username.lower()
            
            if username_lower not in self.accounts:
                raise ValueError("Gebruikersnaam of wachtwoord incorrect")
            
            account = self.accounts[username_lower]
            password_hash = self.hash_password(password)
            
            if account['passwordHash'] != password_hash:
                raise ValueError("Gebruikersnaam of wachtwoord incorrect")
            
            # Update last login
            account['lastLogin'] = datetime.now().isoformat()
            self.save_accounts()
            
            return account
    
    def update_stats(self, username, score, decade, play_time=0, correct_answers=0, total_questions=100):
        """Update user statistics"""
        with self.lock:
            username_lower = username.lower()
            
            if username_lower not in self.accounts:
                raise ValueError("Account niet gevonden")
            
            account = self.accounts[username_lower]
            
            # Update general stats
            account['totalScore'] += score
            account['gamesPlayed'] += 1
            account['totalPlayTime'] = account.get('totalPlayTime', 0) + play_time
            account['lastPlayed'] = datetime.now().isoformat()
            
            # Calculate experience
            experience_gained = score // 10 + correct_answers
            account['experience'] = account.get('experience', 0) + experience_gained
            
            # Calculate level
            new_level = (account['experience'] // 1000) + 1
            account['level'] = new_level
            
            # Update decade stats
            if 'decadeScores' not in account:
                account['decadeScores'] = {}
            
            if decade not in account['decadeScores']:
                account['decadeScores'][decade] = {
                    'totalScore': 0,
                    'gamesPlayed': 0,
                    'bestScore': 0,
                    'totalPlayTime': 0,
                    'totalCorrectAnswers': 0,
                    'totalQuestions': 0,
                    'games': []
                }
            
            decade_stats = account['decadeScores'][decade]
            decade_stats['totalScore'] += score
            decade_stats['gamesPlayed'] += 1
            decade_stats['totalPlayTime'] += play_time
            decade_stats['totalCorrectAnswers'] += correct_answers
            decade_stats['totalQuestions'] += total_questions
            
            # Add game to history
            decade_stats['games'].append({
                'score': score,
                'correctAnswers': correct_answers,
                'totalQuestions': total_questions,
                'playTime': play_time,
                'date': datetime.now().isoformat()
            })
            
            # Update best scores
            if score > decade_stats['bestScore']:
                decade_stats['bestScore'] = score
            
            if score > account.get('bestScore', 0):
                account['bestScore'] = score
                account['bestDecade'] = decade
            
            # Check achievements
            self.check_achievements(account, score, decade, correct_answers, total_questions)
            
            self.save_accounts()
            return account
    
    def check_achievements(self, account, score, decade, correct_answers, total_questions):
        """Check and award achievements"""
        achievements = account.get('achievements', [])
        
        # First game
        if account['gamesPlayed'] == 1 and 'first_game' not in achievements:
            achievements.append('first_game')
        
        # Perfect score
        if correct_answers == total_questions and 'perfect_score' not in achievements:
            achievements.append('perfect_score')
        
        # High score
        if score >= 80 and 'high_score' not in achievements:
            achievements.append('high_score')
        
        # Decade master
        played_decades = len(account.get('decadeScores', {}))
        if played_decades >= 4 and 'decade_master' not in achievements:
            achievements.append('decade_master')
        
        # Dedicated player
        if account['gamesPlayed'] >= 10 and 'dedicated_player' not in achievements:
            achievements.append('dedicated_player')
        
        account['achievements'] = achievements
    
    def get_leaderboard(self):
        """Get leaderboard"""
        with self.lock:
            users = [acc for acc in self.accounts.values() if acc.get('gamesPlayed', 0) > 0]
            users.sort(key=lambda x: x.get('totalScore', 0), reverse=True)
            
            leaderboard = []
            for i, user in enumerate(users[:10]):
                leaderboard.append({
                    'rank': i + 1,
                    'username': user['username'],
                    'totalScore': user.get('totalScore', 0),
                    'gamesPlayed': user.get('gamesPlayed', 0),
                    'level': user.get('level', 1),
                    'bestScore': user.get('bestScore', 0),
                    'bestDecade': user.get('bestDecade')
                })
            
            return leaderboard

class AccountHandler(BaseHTTPRequestHandler):
    account_manager = AccountManager()
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path == '/api/register':
                account = self.account_manager.create_account(data['username'], data['password'])
                self.send_json_response(200, {'success': True, 'account': account})
            
            elif path == '/api/login':
                account = self.account_manager.login(data['username'], data['password'])
                self.send_json_response(200, {'success': True, 'account': account})
            
            elif path == '/api/update-stats':
                account = self.account_manager.update_stats(
                    data['username'], data['score'], data['decade'],
                    data.get('playTime', 0), data.get('correctAnswers', 0), 
                    data.get('totalQuestions', 100)
                )
                self.send_json_response(200, {'success': True, 'account': account})
            
            else:
                self.send_json_response(404, {'error': 'Endpoint not found'})
        
        except ValueError as e:
            self.send_json_response(400, {'error': str(e)})
        except Exception as e:
            print(f"Server error: {e}")
            self.send_json_response(500, {'error': 'Server error'})
    
    def do_GET(self):
        """Handle GET requests"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            path = urlparse(self.path).path
            
            if path == '/api/leaderboard':
                leaderboard = self.account_manager.get_leaderboard()
                self.send_json_response(200, {'success': True, 'leaderboard': leaderboard})
            
            elif path == '/api/status':
                account_count = len(self.account_manager.accounts)
                self.send_json_response(200, {
                    'success': True, 
                    'status': 'running',
                    'accounts': account_count,
                    'file': self.account_manager.accounts_file
                })
            
            else:
                self.send_json_response(404, {'error': 'Endpoint not found'})
        
        except Exception as e:
            print(f"Server error: {e}")
            self.send_json_response(500, {'error': 'Server error'})
    
    def send_json_response(self, status_code, data):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Custom log format"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def main():
    port = 8081
    server = HTTPServer(('0.0.0.0', port), AccountHandler)
    
    print(f"🔐 MorriQuiz Account Server starting on port {port}")
    print(f"📁 Accounts file: {AccountHandler.account_manager.accounts_file}")
    print(f"👥 Loaded {len(AccountHandler.account_manager.accounts)} accounts")
    print(f"🌐 API endpoints:")
    print(f"   POST /api/register - Create account")
    print(f"   POST /api/login - Login")
    print(f"   POST /api/update-stats - Update user stats")
    print(f"   GET /api/leaderboard - Get leaderboard")
    print(f"   GET /api/status - Server status")
    print(f"🚀 Server ready!")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopping...")
        server.shutdown()

if __name__ == '__main__':
    main()
