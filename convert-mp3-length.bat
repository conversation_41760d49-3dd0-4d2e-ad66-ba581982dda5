@echo off
title MorriQuiz MP3 Length Converter
color 0B
cls
echo.
echo  🎵 MorriQuiz MP3 Length Converter v1.0
echo  ═══════════════════════════════════════════════════════════════════════
echo.
echo  This tool converts MP3 files to optimal length for quiz gameplay:
echo  ├── Trims songs to 30-45 seconds (quiz-friendly length)
echo  ├── Keeps the most recognizable part (usually chorus)
echo  ├── Maintains original audio quality
echo  ├── Creates backup of original files
echo  └── Processes all decades automatically
echo.

REM Check if FFmpeg is available
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo  ❌ ERROR: FFmpeg not found!
    echo.
    echo  FFmpeg is required for audio processing.
    echo  Please download from: https://ffmpeg.org/download.html
    echo.
    echo  Installation steps:
    echo  1. Download FFmpeg for Windows
    echo  2. Extract to C:\ffmpeg\
    echo  3. Add C:\ffmpeg\bin to your PATH environment variable
    echo  4. Restart this script
    echo.
    pause
    exit /b 1
)

echo  📊 System Check: FFmpeg ✅
echo.

REM Check if music directory exists
if not exist "music" (
    echo  ❌ ERROR: Music directory not found!
    echo  Please make sure you're running this from the MorriQuiz root directory.
    echo.
    pause
    exit /b 1
)

echo  📁 Music Directory: Found ✅
echo.

echo  🎯 Conversion Settings:
echo  ├── Target Length: 30-45 seconds
echo  ├── Start Position: 25%% into song (to catch chorus)
echo  ├── Audio Quality: Original bitrate maintained
echo  ├── Format: MP3 (unchanged)
echo  └── Backup: Original files saved as .original
echo.

echo  ⚠️  WARNING: This will modify your MP3 files!
echo  Original files will be backed up with .original extension.
echo.
set /p confirm="Do you want to continue? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo  Operation cancelled.
    pause
    exit /b 0
)

echo.
echo  🚀 Starting MP3 conversion process...
echo  ═══════════════════════════════════════════════════════════════════════
echo.

set total_processed=0
set total_converted=0
set total_errors=0

REM Process each decade
for %%d in (60s 70s 80s 90s 00s 10s 20s) do (
    if exist "music\%%d" (
        echo  📂 Processing decade: %%d
        call :process_decade "%%d"
        echo.
    ) else (
        echo  ⚠️  Skipping %%d: Directory not found
    )
)

echo  ═══════════════════════════════════════════════════════════════════════
echo  🎉 Conversion Complete!
echo.
echo  📊 Summary:
echo  ├── Total files processed: %total_processed%
echo  ├── Successfully converted: %total_converted%
echo  ├── Errors encountered: %total_errors%
echo  └── Backup files created: %total_converted%
echo.
echo  💡 Next Steps:
echo  ├── Test the quiz to ensure audio quality
echo  ├── Original files are saved as .original (can be restored)
echo  ├── Delete .original files once satisfied with results
echo  └── Run start-server.bat to test the quiz
echo.
pause
exit /b 0

:process_decade
set decade=%~1
set decade_processed=0
set decade_converted=0
set decade_errors=0

echo     🎵 Scanning %decade% directory...

for %%f in ("music\%decade%\*.mp3") do (
    set /a decade_processed+=1
    set /a total_processed+=1
    
    REM Check if already processed (backup exists)
    if exist "%%f.original" (
        echo     ⏭️  Skipping: %%~nxf (already processed)
    ) else (
        echo     🔄 Converting: %%~nxf
        
        REM Create backup
        copy "%%f" "%%f.original" >nul 2>&1
        if errorlevel 1 (
            echo     ❌ Failed to backup: %%~nxf
            set /a decade_errors+=1
            set /a total_errors+=1
        ) else (
            REM Convert the file (start at 25% of duration, take 35 seconds)
            ffmpeg -i "%%f.original" -ss 00:00:20 -t 00:00:35 -acodec copy "%%f.temp" -y >nul 2>&1
            if errorlevel 1 (
                echo     ❌ Conversion failed: %%~nxf
                del "%%f.original" >nul 2>&1
                set /a decade_errors+=1
                set /a total_errors+=1
            ) else (
                REM Replace original with converted
                move "%%f.temp" "%%f" >nul 2>&1
                if errorlevel 1 (
                    echo     ❌ File replacement failed: %%~nxf
                    move "%%f.original" "%%f" >nul 2>&1
                    set /a decade_errors+=1
                    set /a total_errors+=1
                ) else (
                    echo     ✅ Converted: %%~nxf
                    set /a decade_converted+=1
                    set /a total_converted+=1
                )
            )
        )
    )
)

echo     📊 %decade% Results: %decade_processed% processed, %decade_converted% converted, %decade_errors% errors
goto :eof
