/* MorriQuiz - Quiz Page Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  padding-bottom: 60px;
}

/* Loader */
#loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 8px solid #fff;
  animation: l20-1 0.8s infinite linear alternate, l20-2 1.6s infinite linear;
}

@keyframes l20-1 {
  0% { clip-path: polygon(50% 50%, 0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%); }
  12.5% { clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%); }
  25% { clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%); }
  50% { clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%); }
  62.5% { clip-path: polygon(50% 50%, 100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%); }
  75% { clip-path: polygon(50% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%); }
  100% { clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%); }
}

@keyframes l20-2 {
  0% { transform: scaleY(1) rotate(0deg); }
  49.99% { transform: scaleY(1) rotate(135deg); }
  50% { transform: scaleY(-1) rotate(0deg); }
  100% { transform: scaleY(-1) rotate(-135deg); }
}

.hidden {
  display: none !important;
}

/* Header */
.quiz-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.header-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-logo-img {
  max-width: 80px;
  max-height: 80px;
  width: auto;
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Header h1 styling removed as text is no longer used */

.quiz-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.quiz-info span {
  background: rgba(102, 126, 234, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Quiz Container */
.quiz-container {
  max-width: 1000px;
  margin: 30px auto;
  padding: 0 20px;
}

/* Audio Section */
.audio-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.audio-player {
  text-align: center;
}

#quiz-audio {
  width: 100%;
  max-width: 500px;
  height: 60px;
  border-radius: 30px;
  background: #f8f9fa;
  margin-bottom: 15px;
}

#quiz-audio::-webkit-media-controls-panel {
  background-color: #667eea;
  border-radius: 30px;
}

.audio-info {
  color: #666;
  font-style: italic;
  margin-bottom: 15px;
}

.audio-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.audio-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: bold;
}

.audio-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.audio-btn:active {
  transform: translateY(0);
}

/* Question Section */
.question-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Progress Bar */
.progress-bar {
  position: relative;
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  margin-bottom: 30px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 10%;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  color: #666;
  font-weight: bold;
}

/* Question */
#question-text {
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.4;
}

/* Options */
.options-container {
  margin-top: 20px;
}

.options {
  list-style: none;
  display: grid;
  gap: 15px;
}

.options li {
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid transparent;
  border-radius: 15px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

.options li:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateX(5px);
  border-color: #667eea;
}

.option-label {
  background: #667eea;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.option-text {
  flex-grow: 1;
  font-size: 1.1rem;
  line-height: 1.4;
}

.option-icon {
  width: 30px;
  height: 30px;
  flex-shrink: 0;
}

/* Correct/Incorrect states */
.options li.correct {
  background: rgba(40, 167, 69, 0.2);
  border-color: #28a745;
  color: #155724;
}

.options li.correct .option-label {
  background: #28a745;
}

.options li.incorrect {
  background: rgba(220, 53, 69, 0.2);
  border-color: #dc3545;
  color: #721c24;
}

.options li.incorrect .option-label {
  background: #dc3545;
}

/* Feedback Section */
.feedback-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
}

.feedback-message {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 20px;
}

.feedback-message.correct {
  color: #28a745;
}

.feedback-message.incorrect {
  color: #dc3545;
}

.explanation {
  background: rgba(102, 126, 234, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 25px;
  line-height: 1.6;
}

.next-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.next-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* Navigation */
.navigation {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  flex-wrap: wrap;
}

.nav-btn {
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-weight: bold;
}

.nav-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.nav-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: 2px solid #667eea;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-content h2 {
  color: #667eea;
  margin-bottom: 25px;
  font-size: 2rem;
}

.final-score {
  margin: 30px 0;
}

.final-score span {
  display: block;
  font-size: 3rem;
  font-weight: bold;
  color: #667eea;
  margin: 10px 0;
}

.score-breakdown {
  background: rgba(102, 126, 234, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin: 25px 0;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 30px;
}

.primary-btn, .secondary-btn {
  padding: 12px 25px;
  border-radius: 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-weight: bold;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.secondary-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2px solid #667eea;
}

.primary-btn:hover, .secondary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Footer */
.footer {
  background: rgba(51, 51, 51, 0.9);
  color: #ffffff;
  text-align: center;
  padding: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  backdrop-filter: blur(10px);
}

.footer p {
  margin: 0;
  font-size: 0.9em;
  color: #bbb;
}

.footer a {
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-logo-img {
    max-width: 60px;
    max-height: 60px;
  }

  .header-logo {
    gap: 10px;
  }

  .quiz-info {
    justify-content: center;
  }
  
  .quiz-container {
    padding: 0 15px;
  }
  
  .audio-section, .question-section, .feedback-section {
    padding: 20px;
  }
  
  #question-text {
    font-size: 1.3rem;
  }
  
  .options li {
    padding: 15px;
  }
  
  .navigation {
    justify-content: center;
  }
  
  .modal-content {
    padding: 30px 20px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
