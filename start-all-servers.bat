@echo off
title MorriQuiz Complete Server Suite
color 0A
cls
echo.
echo  ███╗   ███╗ ██████╗ ██████╗ ██████╗ ██╗ ██████╗ ██╗   ██╗██╗███████╗
echo  ████╗ ████║██╔═══██╗██╔══██╗██╔══██╗██║██╔═══██╗██║   ██║██║╚══███╔╝
echo  ██╔████╔██║██║   ██║██████╔╝██████╔╝██║██║   ██║██║   ██║██║  ███╔╝ 
echo  ██║╚██╔╝██║██║   ██║██╔══██╗██╔══██╗██║██║▄▄ ██║██║   ██║██║ ███╔╝  
echo  ██║ ╚═╝ ██║╚██████╔╝██║  ██║██║  ██║██║╚██████╔╝╚██████╔╝██║███████╗
echo  ╚═╝     ╚═╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝ ╚══▀▀═╝  ╚═════╝ ╚═╝╚══════╝
echo.
echo  🎵 Complete MorriQuiz Server Suite v3.0
echo  ═══════════════════════════════════════════════════════════════════════
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo  ❌ ERROR: Python not found!
    echo  Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

echo  📊 System Check: Python ✅
echo.

REM Get network info
echo  🔍 Checking network configuration...
for /f "tokens=1" %%i in ('tailscale status 2^>nul ^| findstr /C:"morflix"') do set TAILSCALE_IP=%%i

if "%TAILSCALE_IP%"=="" (
    echo  ⚠️  Tailscale: Not connected
    set TAILSCALE_IP=Not Available
) else (
    echo  🔗 Tailscale: Connected (%TAILSCALE_IP%)
)

for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /C:"IPv4 Address"') do set LOCAL_IP=%%i
set LOCAL_IP=%LOCAL_IP: =%
echo  🌐 Local Network: %LOCAL_IP%
echo.

echo  🎮 MorriQuiz Features:
echo  ├── 👤 Singleplayer: 100-question quiz per decade
echo  ├── 🔐 Account System: Permanent score storage
echo  ├── 🏆 Achievements: Badge system with levels
echo  ├── 📊 Leaderboard: Cross-device rankings
echo  └── 🎵 Real-time audio streaming
echo.

echo  📁 Music Library Status:
if exist "music\60s" (echo  ├── 60s: ✅ Available) else (echo  ├── 60s: ❌ Missing)
if exist "music\70s" (echo  ├── 70s: ✅ Available) else (echo  ├── 70s: ❌ Missing)
if exist "music\80s" (echo  ├── 80s: ✅ Available) else (echo  ├── 80s: ❌ Missing)
if exist "music\90s" (echo  ├── 90s: ✅ Available) else (echo  ├── 90s: ❌ Missing)
if exist "music\00s" (echo  ├── 00s: ✅ Available) else (echo  ├── 00s: ❌ Missing)
if exist "music\10s" (echo  ├── 10s: ✅ Available) else (echo  ├── 10s: ❌ Missing)
if exist "music\20s" (echo  └── 20s: ✅ Available) else (echo  └── 20s: ❌ Missing)
echo.

echo  🌐 Access URLs:
echo  ═══════════════════════════════════════════════════════════════════════
echo  📱 Main Website:
echo     http://localhost:8080
echo     http://%LOCAL_IP%:8080
if not "%TAILSCALE_IP%"=="Not Available" (
    echo     http://%TAILSCALE_IP%:8080 (Tailscale)
)
echo.
echo  🔐 Account API:
echo     http://localhost:8081/api/status
echo     http://%LOCAL_IP%:8081/api/status
if not "%TAILSCALE_IP%"=="Not Available" (
    echo     http://%TAILSCALE_IP%:8081/api/status (Tailscale)
)
echo.
echo  ═══════════════════════════════════════════════════════════════════════
echo  🚀 Starting servers...
echo.

REM Start Account Server in background
echo  🔐 Starting Account Server (Port 8081)...
start /min "MorriQuiz Account Server" python account-server.py

REM Wait a moment for account server to start
timeout /t 2 /nobreak >nul

REM Start Web Server
echo  🌐 Starting Web Server (Port 8080)...
echo.
echo  📊 Both servers are now running!
echo  🕐 Started: %date% %time%
echo.
echo  💡 Tips:
echo  ├── Use Ctrl+C to stop the web server
echo  ├── Account server runs in background window
echo  ├── Close all windows to stop both servers
echo  ├── Check accounts.json for saved user data
echo  └── Use debug.html for troubleshooting
echo.
echo  ═══════════════════════════════════════════════════════════════════════
echo.

REM Start the web server (this will block)
if exist music-server.py (
    echo  🎵 Starting MorriQuiz Music Server with API support...
    python music-server.py
) else (
    echo  🌐 Starting basic HTTP server...
    python -m http.server 8080 --bind 0.0.0.0
)
