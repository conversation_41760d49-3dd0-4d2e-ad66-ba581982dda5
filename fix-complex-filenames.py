#!/usr/bin/env python3
"""
MorriQuiz Complex Filename Fixer
Fixes filenames that have extra information beyond the standard "Artist - Title - Year.mp3" format
"""

import os
import re
from pathlib import Path

def fix_complex_filename(filename):
    """Fix complex filenames to standard format."""
    # Remove extension
    name = filename.replace('.mp3', '')
    parts = name.split(' - ')
    
    if len(parts) < 3:
        return filename  # Can't fix, not enough parts
    
    if len(parts) == 3:
        return filename  # Already correct format
    
    # Handle complex cases
    artist = parts[0].strip()
    year = parts[-1].strip()
    
    # Combine middle parts as title, but clean up common patterns
    title_parts = parts[1:-1]
    title = ' - '.join(title_parts)
    
    # Clean up common patterns in titles
    title = re.sub(r'\s*-\s*[^-]*(?:Version|Edit|Remix|Radio)\s*$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*-\s*[^-]*(?:Vs\.|vs)\s*[^-]*$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\([^)]*(?:Version|Edit|Remix|Radio)[^)]*\)\s*$', '', title, flags=re.IGNORECASE)
    
    # Remove extra quotes and clean up
    title = title.strip(' -"\'')
    
    # Create new filename
    new_filename = f"{artist} - {title} - {year}.mp3"
    
    return new_filename

def fix_files_in_directory(decade_dir):
    """Fix all problematic MP3 files in a decade directory."""
    decade = decade_dir.name
    mp3_files = list(decade_dir.glob("*.mp3"))
    
    if not mp3_files:
        print(f"No MP3 files found in {decade_dir}")
        return
    
    print(f"\n=== Fixing complex filenames in {decade} ===")
    
    fixed_count = 0
    
    for mp3_file in mp3_files:
        original_name = mp3_file.name
        
        # Check if filename has more than 3 parts (complex)
        name_parts = original_name.replace('.mp3', '').split(' - ')
        if len(name_parts) <= 3:
            continue  # Skip files that are already in correct format
        
        # Generate new filename
        new_name = fix_complex_filename(original_name)
        
        if new_name == original_name:
            continue  # No change needed
        
        new_path = decade_dir / new_name
        
        # Check if new filename already exists
        counter = 1
        while new_path.exists():
            base_name = new_name.replace('.mp3', '')
            new_name = f"{base_name} ({counter}).mp3"
            new_path = decade_dir / new_name
            counter += 1
        
        try:
            # Rename the file
            mp3_file.rename(new_path)
            print(f"✓ Fixed: {original_name}")
            print(f"     To: {new_name}")
            fixed_count += 1
        except Exception as e:
            print(f"✗ Error fixing {original_name}: {e}")
    
    print(f"Fixed {fixed_count} complex filenames in {decade}")

def main():
    """Main function to fix complex filenames in all music directories."""
    music_dir = Path("music")
    
    if not music_dir.exists():
        print("Error: music/ directory not found!")
        return
    
    print("🎵 MorriQuiz Complex Filename Fixer")
    print("=" * 50)
    print("Fixing filenames with extra information to standard format")
    
    # Process each decade directory
    for decade in ['60s', '70s', '80s', '90s', '00s', '10s', '20s']:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            fix_files_in_directory(decade_dir)
        else:
            print(f"Directory {decade} not found, skipping...")
    
    print("\n" + "=" * 50)
    print("✅ Complex filename fixing complete!")
    print("All files should now be in format: Artist - Title - Year.mp3")

if __name__ == "__main__":
    main()
