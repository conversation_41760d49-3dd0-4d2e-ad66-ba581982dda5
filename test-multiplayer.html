<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiplayer Test</title>
    <link rel="icon" type="image/png" href="logo.png">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .test-section { background: rgba(255,255,255,0.1); padding: 20px; margin: 20px 0; border-radius: 15px; }
        button { background: #4facfe; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin: 5px; }
        button:hover { background: #3b82f6; }
        .result { background: rgba(255,255,255,0.2); padding: 10px; margin: 10px 0; border-radius: 8px; }
        input { padding: 8px; border-radius: 5px; border: none; margin: 5px; }
    </style>
</head>
<body>
    <h1>🎮 Multiplayer System Test</h1>
    
    <div class="test-section">
        <h3>1. Room Creation Test</h3>
        <input type="text" id="test-player-name" placeholder="Player Name" value="TestPlayer">
        <select id="test-decade">
            <option value="10s">10s</option>
            <option value="00s">00s</option>
            <option value="90s">90s</option>
            <option value="80s">80s</option>
        </select>
        <button onclick="testCreateRoom()">Create Room</button>
        <div id="create-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Room Join Test</h3>
        <input type="text" id="join-room-code" placeholder="Room Code" maxlength="6">
        <input type="text" id="join-player-name" placeholder="Player Name" value="Player2">
        <button onclick="testJoinRoom()">Join Room</button>
        <div id="join-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Room Info Test</h3>
        <button onclick="testRoomInfo()">Get Room Info</button>
        <div id="room-info-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Music Loading Test</h3>
        <button onclick="testMusicLoading()">Test Music Loading</button>
        <div id="music-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. Storage Test</h3>
        <button onclick="testStorage()">Test localStorage</button>
        <div id="storage-result" class="result"></div>
    </div>

    <script src="js/multiplayer.js?v=9"></script>
    <script src="js/musicLoader.js?v=9"></script>
    <script>
        function testCreateRoom() {
            const playerName = document.getElementById('test-player-name').value;
            const decade = document.getElementById('test-decade').value;
            const resultDiv = document.getElementById('create-result');
            
            try {
                const roomCode = window.MultiplayerManager.createRoom(playerName, decade);
                resultDiv.innerHTML = `
                    <strong>✅ Room Created Successfully!</strong><br>
                    Room Code: <strong>${roomCode}</strong><br>
                    Player: ${playerName}<br>
                    Decade: ${decade}<br>
                    Is Host: ${window.MultiplayerManager.isHost}
                `;
                resultDiv.style.background = 'rgba(34, 197, 94, 0.3)';
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.style.background = 'rgba(239, 68, 68, 0.3)';
            }
        }
        
        function testJoinRoom() {
            const roomCode = document.getElementById('join-room-code').value;
            const playerName = document.getElementById('join-player-name').value;
            const resultDiv = document.getElementById('join-result');
            
            try {
                const room = window.MultiplayerManager.joinRoom(roomCode, playerName);
                resultDiv.innerHTML = `
                    <strong>✅ Joined Room Successfully!</strong><br>
                    Room Code: ${roomCode}<br>
                    Player: ${playerName}<br>
                    Host: ${room.host}<br>
                    Players: ${Object.keys(room.players).length}
                `;
                resultDiv.style.background = 'rgba(34, 197, 94, 0.3)';
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.style.background = 'rgba(239, 68, 68, 0.3)';
            }
        }
        
        function testRoomInfo() {
            const resultDiv = document.getElementById('room-info-result');
            
            try {
                const room = window.MultiplayerManager.getRoomInfo();
                if (room) {
                    resultDiv.innerHTML = `
                        <strong>✅ Room Info Retrieved!</strong><br>
                        Code: ${room.code}<br>
                        Host: ${room.host}<br>
                        Decade: ${room.decade}<br>
                        Players: ${Object.keys(room.players).length}/${room.maxPlayers}<br>
                        State: ${room.gameState}<br>
                        Questions: ${room.questions.length}
                    `;
                    resultDiv.style.background = 'rgba(34, 197, 94, 0.3)';
                } else {
                    resultDiv.innerHTML = '<strong>⚠️ No active room</strong>';
                    resultDiv.style.background = 'rgba(251, 191, 36, 0.3)';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.style.background = 'rgba(239, 68, 68, 0.3)';
            }
        }
        
        async function testMusicLoading() {
            const resultDiv = document.getElementById('music-result');
            
            try {
                if (!window.MusicLoader) {
                    throw new Error('MusicLoader not available');
                }
                
                const songs = await window.MusicLoader.loadDecadeMusic('10s');
                resultDiv.innerHTML = `
                    <strong>✅ Music Loaded Successfully!</strong><br>
                    Decade: 10s<br>
                    Songs: ${songs.length}<br>
                    Sample: ${songs[0] ? songs[0].artist + ' - ' + songs[0].title : 'None'}
                `;
                resultDiv.style.background = 'rgba(34, 197, 94, 0.3)';
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.style.background = 'rgba(239, 68, 68, 0.3)';
            }
        }
        
        function testStorage() {
            const resultDiv = document.getElementById('storage-result');
            
            try {
                // Test localStorage
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('morriQuizTest', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('morriQuizTest'));
                
                // Test rooms storage
                const rooms = window.MultiplayerManager.loadRooms();
                
                resultDiv.innerHTML = `
                    <strong>✅ Storage Working!</strong><br>
                    localStorage: Available<br>
                    Test data: ${retrieved.test}<br>
                    Rooms in storage: ${Object.keys(rooms).length}<br>
                    Current room: ${window.MultiplayerManager.currentRoom || 'None'}
                `;
                resultDiv.style.background = 'rgba(34, 197, 94, 0.3)';
                
                // Cleanup
                localStorage.removeItem('morriQuizTest');
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.style.background = 'rgba(239, 68, 68, 0.3)';
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            console.log('🎮 Multiplayer Test Page Loaded');
            console.log('MultiplayerManager:', window.MultiplayerManager);
            console.log('MusicLoader:', window.MusicLoader);
        });
    </script>
</body>
</html>
