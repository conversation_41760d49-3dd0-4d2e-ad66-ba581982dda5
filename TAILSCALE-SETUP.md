# 🌐 MorriQuiz Tailscale Setup

## Snelle Start

### 1. Server Starten
Dubbelklik op een van deze bestanden:
- `start-server.bat` (Windows Command Prompt)
- `start-server.ps1` (PowerShell - aanbevolen)

### 2. URLs Gebruiken
Na het starten zie je deze URLs:

**Lokale toegang:**
- `http://localhost:8080`
- `http://[je-lokale-ip]:8080`

**Tailscale toegang:**
- `http://*************:8080` (jouw huidige Tailscale IP)

## 📱 Toegang vanaf Andere Apparaten

### Op je telefoon/tablet (met Tailscale):
1. Zorg dat Tailscale app is geïnstalleerd en ingelogd
2. Open browser en ga naar: `http://*************:8080`
3. Geniet van MorriQuiz op je mobiele apparaat!

### Op andere computers (met Tailscale):
1. Zorg dat Tailscale is geïnstalleerd en ingelogd
2. Open browser en ga naar: `http://*************:8080`
3. Speel MorriQuiz vanaf elk apparaat in je netwerk!

## 🔧 Handmatige Setup

Als de automatische scripts niet werken:

```bash
# 1. Controleer Tailscale status
tailscale status

# 2. Start server op alle interfaces
python -m http.server 8080 --bind 0.0.0.0

# 3. Ga naar je Tailscale IP in browser
# http://[je-tailscale-ip]:8080
```

## 🛠️ Troubleshooting

### Server start niet:
- Controleer of Python is geïnstalleerd: `python --version`
- Probeer: `py -m http.server 8080 --bind 0.0.0.0`
- Controleer of poort 8080 vrij is: `netstat -an | findstr :8080`

### Tailscale IP niet gevonden:
- Controleer of Tailscale draait: `tailscale status`
- Herstart Tailscale service
- Log opnieuw in bij Tailscale

### Kan niet verbinden via Tailscale:
- Controleer firewall instellingen
- Zorg dat beide apparaten in hetzelfde Tailscale netwerk zitten
- Probeer een andere poort: `python -m http.server 8081 --bind 0.0.0.0`

## 🎯 Voordelen van Tailscale

- **Veilig**: Automatische end-to-end encryptie
- **Eenvoudig**: Geen poort forwarding of router configuratie
- **Cross-platform**: Werkt op Windows, Mac, Linux, iOS, Android
- **Privé**: Alleen jouw apparaten hebben toegang
- **Snel**: Directe peer-to-peer verbindingen waar mogelijk

## 📋 Huidige Configuratie

- **Server IP**: ************* (Tailscale)
- **Poort**: 8080
- **Protocol**: HTTP
- **Toegang**: Alle interfaces (0.0.0.0)

## 🔄 Server Stoppen

Druk `Ctrl+C` in het terminal venster om de server te stoppen.

---

**Tip**: Bookmark `http://*************:8080` op al je apparaten voor snelle toegang tot MorriQuiz! 🎵
