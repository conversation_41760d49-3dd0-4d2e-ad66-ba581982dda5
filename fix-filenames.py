#!/usr/bin/env python3
"""
MorriQuiz Filename Fixer
Converts existing MP3 filenames to the required format: "Artist - Title - Year.mp3"
"""

import os
import re
import shutil
from pathlib import Path

# Mapping of decades to year ranges for automatic year assignment
DECADE_YEARS = {
    '60s': (1960, 1969),
    '70s': (1970, 1979),
    '80s': (1980, 1989),
    '90s': (1990, 1999),
    '00s': (2000, 2009),
    '10s': (2010, 2019),
    '20s': (2020, 2029)
}

def clean_filename(filename):
    """Clean filename by removing unwanted characters and patterns."""
    # Remove file extension
    name = filename.replace('.mp3', '')
    
    # Remove common patterns
    patterns_to_remove = [
        r'\s*-\s*Radio Edit',
        r'\s*-\s*Remix',
        r'\s*\(feat\.\s*[^)]+\)',
        r'\s*\(with\s*[^)]+\)',
        r'\s*\([^)]*Remix[^)]*\)',
        r'\s*\([^)]*Radio[^)]*\)',
        r'\s*\([^)]*Edit[^)]*\)',
        r'\s*\([^)]*Version[^)]*\)',
    ]
    
    for pattern in patterns_to_remove:
        name = re.sub(pattern, '', name, flags=re.IGNORECASE)
    
    return name.strip()

def parse_filename(filename):
    """Try to parse artist and title from filename."""
    clean_name = clean_filename(filename)
    
    # Try different separators
    separators = [' - ', ', ', ' feat. ', ' & ']
    
    for sep in separators:
        if sep in clean_name:
            parts = clean_name.split(sep, 1)
            if len(parts) == 2:
                artist = parts[0].strip()
                title = parts[1].strip()
                
                # Clean up common issues
                artist = re.sub(r'^(The\s+)?(.+)', r'\2', artist)  # Remove "The" prefix
                title = re.sub(r'\s*\([^)]*\)$', '', title)  # Remove trailing parentheses
                
                return artist, title
    
    # If no separator found, assume it's all title with unknown artist
    return "Unknown Artist", clean_name

def get_default_year(decade):
    """Get a default year for the decade (middle year)."""
    start, end = DECADE_YEARS[decade]
    return start + (end - start) // 2

def fix_filenames_in_directory(decade_dir):
    """Fix all MP3 filenames in a decade directory."""
    decade = decade_dir.name
    mp3_files = list(decade_dir.glob("*.mp3"))
    
    if not mp3_files:
        print(f"No MP3 files found in {decade_dir}")
        return
    
    print(f"\n=== Fixing filenames in {decade} ===")
    print(f"Found {len(mp3_files)} MP3 files")
    
    default_year = get_default_year(decade)
    fixed_count = 0
    
    for mp3_file in mp3_files:
        original_name = mp3_file.name
        
        # Check if already in correct format
        if re.match(r'^.+ - .+ - \d{4}\.mp3$', original_name):
            print(f"✓ Already correct: {original_name}")
            continue
        
        # Parse artist and title
        artist, title = parse_filename(original_name)
        
        # Create new filename
        new_name = f"{artist} - {title} - {default_year}.mp3"
        new_path = decade_dir / new_name
        
        # Check if new filename already exists
        counter = 1
        while new_path.exists():
            new_name = f"{artist} - {title} - {default_year} ({counter}).mp3"
            new_path = decade_dir / new_name
            counter += 1
        
        try:
            # Rename the file
            mp3_file.rename(new_path)
            print(f"✓ Renamed: {original_name}")
            print(f"     To: {new_name}")
            fixed_count += 1
        except Exception as e:
            print(f"✗ Error renaming {original_name}: {e}")
    
    print(f"Fixed {fixed_count} files in {decade}")

def main():
    """Main function to fix filenames in all music directories."""
    music_dir = Path("music")
    
    if not music_dir.exists():
        print("Error: music/ directory not found!")
        return
    
    print("🎵 MorriQuiz Filename Fixer")
    print("=" * 50)
    print("Converting filenames to format: Artist - Title - Year.mp3")
    
    # Process each decade directory
    for decade in ['60s', '70s', '80s', '90s', '00s', '10s', '20s']:
        decade_dir = music_dir / decade
        if decade_dir.exists():
            fix_filenames_in_directory(decade_dir)
        else:
            print(f"Directory {decade} not found, skipping...")
    
    print("\n" + "=" * 50)
    print("✅ Filename fixing complete!")
    print("You can now start the quiz with properly formatted filenames.")
    print("\nTo start the server: python music-server.py")

if __name__ == "__main__":
    main()
