# 🎵 MorriQuiz - Muziek Quiz Website

Een interactieve muziek quiz website waar je je kennis van verschillende decennia kunt testen!

## ✨ Features

- **7 Verschillende Decennia**: Van <PERSON> jar<PERSON> 60 tot de jaren 20
- **100 Vragen per Quiz**: Uitgebreide quiz sessies voor maximale uitdaging
- **Automatische Opslag**: Alle voortgang wordt automatisch opgeslagen
- **Se<PERSON>**: <PERSON><PERSON><PERSON> en hervat je quiz op elk moment
- **Interactieve Audio**: Luister naar muziekfragmenten tijdens de quiz
- **Nederlandse Interface**: Volledig in het Nederlands
- **Gebruiker Systeem**: Voer je naam in en houd je voortgang bij
- **Leaderboard**: Bekijk de beste scores van alle spelers
- **Gedetailleerde Statistieken**: Track je antwoordgeschiedenis
- **Responsive Design**: Werkt op desktop, tablet en mobiel
- **4 Antwoordopties**: Elke vraag heeft 1 correct en 3 foute antwoorden
- **Random Vragen**: Over artiest, titel of jaar van uitbrenging

## 🚀 Installatie

1. **Download de bestanden**:
   - Clone deze repository of download als ZIP
   - Pak uit in een map naar keuze

2. **Muziek toevoegen** (VERPLICHT):
   - Ga naar de `music/` map
   - Plaats MP3 bestanden in de juiste decade mappen (60s, 70s, 80s, 90s, 00s, 10s, 20s)
   - Gebruik het formaat: `Artiest - Titel - Jaar.mp3`
   - **Minimaal 10 bestanden per decade** voor optimale ervaring

3. **Website starten**:
   - **Aanbevolen**: Dubbelklik op `start-server.ps1` (gebruikt music-server.py)
   - **Alternatief**: Dubbelklik op `start-server.bat`
   - **Handmatig**: `python music-server.py`

## 📁 Mappenstructuur

```
MorriQuiz/
├── index.html              # Hoofdpagina met naam invoer en decade selectie
├── css/
│   ├── index.css           # Styling voor hoofdpagina
│   └── quiz.css            # Styling voor quiz pagina
├── js/
│   ├── script.js           # Hoofdfunctionaliteit en gebruiker systeem
│   ├── musicLoader.js      # Muziek laden en vraag generatie
│   └── quiz.js             # Quiz logica en gameplay
├── quiz/
│   └── quiz.html           # Quiz pagina
├── music/                  # Muziek bestanden per decade
│   ├── 60s/               # Jaren 1960-1969
│   ├── 70s/               # Jaren 1970-1979
│   ├── 80s/               # Jaren 1980-1989
│   ├── 90s/               # Jaren 1990-1999
│   ├── 00s/               # Jaren 2000-2009
│   ├── 10s/               # Jaren 2010-2019
│   ├── 20s/               # Jaren 2020-2029
│   └── README.md          # Instructies voor muziek bestanden
└── README.md              # Dit bestand
```

## 🎵 Muziek Bestanden Toevoegen

### Bestandsnaam Formaat:
```
Artiest - Titel - Jaar.mp3
```

### Voorbeelden:
```
The Beatles - Hey Jude - 1968.mp3
Queen - Bohemian Rhapsody - 1975.mp3
Michael Jackson - Billie Jean - 1983.mp3
```

### Belangrijke Opmerkingen:
- **Alleen MP3 bestanden** worden ondersteund
- **Exacte naamconventie** is vereist voor automatische verwerking
- **Minimaal 10 nummers** per decade voor optimale ervaring
- **Geen speciale karakters** in bestandsnamen
- **Demo data verwijderd** - echte bestanden zijn nu verplicht

## 🎮 Hoe te Spelen

1. **Naam Invoeren**: Voer je naam in op het startscherm
2. **Decade Kiezen**: Selecteer een decade om te spelen (zie voortgang van lopende quizzes)
3. **Quiz Starten/Hervatten**: Start een nieuwe quiz of hervat een bestaande
4. **Muziek Beluisteren**: Klik op play om het muziekfragment te beluisteren
5. **Vraag Beantwoorden**: Kies uit 4 antwoordopties (100 vragen totaal)
6. **Automatische Opslag**: Je voortgang wordt na elke vraag opgeslagen
7. **Quiz Voltooien**: Behaal je eindscore en bekijk gedetailleerde statistieken
8. **Leaderboard**: Bekijk je positie op het leaderboard

## 🏆 Score Systeem

- **2 punten** per correct antwoord
- **Maximum score**: 200 punten (100 vragen × 2 punten)
- **Geen aftrek** voor foute antwoorden
- **Percentage berekening** aan het einde van de quiz
- **Automatische opslag** na elke vraag
- **Sessie herstel** bij onderbreking

## 💾 Data Opslag

Alle gegevens worden lokaal opgeslagen in je browser:
- **Speler namen** en scores
- **Actieve quiz sessies** met volledige voortgang
- **Antwoordgeschiedenis** per vraag
- **Voltooide quiz resultaten** (laatste 50 per speler)
- **Leaderboard** gegevens
- **Laatste speeldatum** en sessie informatie

## 🔄 Automatische Opslag & Sessie Herstel

- **Na elke vraag**: Voortgang wordt automatisch opgeslagen
- **Bij onderbreking**: Quiz kan later worden hervat vanaf exacte positie
- **Sessie herstel**: Automatische detectie van onvoltooide quizzes
- **Voortgang indicator**: Zie je voortgang per decade op het hoofdscherm
- **Geschiedenis**: Alle voltooide quizzes worden bewaard voor statistieken

## 🔧 Technische Vereisten

- **Moderne webbrowser** (Chrome, Firefox, Safari, Edge)
- **JavaScript ingeschakeld**
- **Lokale bestanden toegang** (voor muziek afspelen)
- **Optioneel**: Lokale webserver voor betere prestaties

## 🌐 Lokale Webserver (Aanbevolen)

Voor betere prestaties en audio ondersteuning:

### Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

### Node.js:
```bash
npx http-server
```

### PHP:
```bash
php -S localhost:8000
```

Ga dan naar `http://localhost:8000` in je browser.

## 🌐 Tailscale Toegang

MorriQuiz is volledig compatibel met Tailscale voor veilige externe toegang:

### Automatische Setup:
```bash
# Windows
start-server.bat

# PowerShell
.\start-server.ps1
```

### Handmatige Setup:
```bash
# Start server op alle interfaces
python -m http.server 8080 --bind 0.0.0.0

# Controleer je Tailscale IP
tailscale status
```

### Toegang via Tailscale:
- **Lokaal**: `http://localhost:8080`
- **Tailscale**: `http://[je-tailscale-ip]:8080`
- **Delen**: Deel de Tailscale URL met anderen in je netwerk

### Voordelen:
- **Veilige toegang** vanaf elk apparaat in je Tailscale netwerk
- **Geen poort forwarding** nodig
- **Automatische encryptie** via Tailscale
- **Cross-platform** toegang (Windows, Mac, Linux, mobiel)

## 🎨 Aanpassingen

### Kleuren Wijzigen:
Bewerk de CSS variabelen in `css/index.css` en `css/quiz.css`

### Meer Decennia Toevoegen:
1. Maak nieuwe map in `music/`
2. Voeg decade toe aan `index.html`
3. Update `musicLoader.js` met nieuwe decade data

### Vraag Types Uitbreiden:
Bewerk de `generateQuestions()` functie in `musicLoader.js`

## 🐛 Troubleshooting

### Audio speelt niet af:
- Controleer of MP3 bestanden in de juiste map staan
- Gebruik een lokale webserver
- Controleer browser console voor fouten

### Quiz laadt niet:
- Controleer bestandsnamen op juiste formaat
- Zorg voor minimaal 10 bestanden per decade
- Herlaad de pagina

### Scores worden niet opgeslagen:
- Controleer of JavaScript is ingeschakeld
- Wis browser cache en probeer opnieuw
- Controleer browser console voor fouten

## 📱 Browser Ondersteuning

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Bijdragen

Wil je bijdragen aan MorriQuiz?
1. Fork het project
2. Maak een feature branch
3. Commit je wijzigingen
4. Push naar de branch
5. Open een Pull Request

## 📄 Licentie

Dit project is open source en beschikbaar onder de MIT Licentie.

## 🎵 Credits

Gebaseerd op de originele Quiz-Website van [Tejas Mali](https://github.com/tejazmali/Quiz-Website)

---

**Veel plezier met MorriQuiz! 🎵🎉**
