// MorriQuiz Multiplayer Quiz JavaScript

class MultiplayerQuiz {
  constructor() {
    this.roomCode = null;
    this.playerName = null;
    this.currentQuestion = 0;
    this.questions = [];
    this.hasAnswered = false;
    this.pollInterval = null;
    this.nextQuestionTimer = null;
    
    this.init();
  }

  init() {
    // Get room and player from URL
    const urlParams = new URLSearchParams(window.location.search);
    this.roomCode = urlParams.get('room');
    this.playerName = urlParams.get('player');
    
    if (!this.roomCode || !this.playerName) {
      alert('Ongeldige multiplayer link');
      window.location.href = '../index.html';
      return;
    }
    
    // Set up multiplayer manager
    window.MultiplayerManager.currentRoom = this.roomCode;
    window.MultiplayerManager.playerName = this.playerName;
    
    // Initialize UI
    document.getElementById('room-code').textContent = this.roomCode;
    
    // Load room data and start
    this.loadRoomData();
    this.startPolling();
  }

  loadRoomData() {
    const room = window.MultiplayerManager.getRoomInfo();
    if (!room) {
      alert('Room niet gevonden');
      window.location.href = '../index.html';
      return;
    }
    
    if (room.gameState !== 'playing') {
      alert('Game is niet actief');
      window.location.href = '../index.html';
      return;
    }
    
    this.questions = room.questions;
    this.currentQuestion = room.currentQuestion;
    
    document.getElementById('total-questions').textContent = this.questions.length;
    
    this.loadCurrentQuestion();
  }

  async loadCurrentQuestion() {
    if (this.currentQuestion >= this.questions.length) {
      this.showFinalResults();
      return;
    }
    
    const question = this.questions[this.currentQuestion];
    document.getElementById('current-question').textContent = this.currentQuestion + 1;
    
    // Reset answer state
    this.hasAnswered = false;
    this.enableAnswerButtons();
    
    // Load audio
    await this.loadAudio(question);
    
    // Generate question
    this.generateQuestion(question);
    
    // Update players status
    this.updatePlayersStatus();
  }

  async loadAudio(song) {
    const audio = document.getElementById('quiz-audio');
    const audioStatus = document.getElementById('audio-status');
    
    audioStatus.textContent = '🎵 Muziek wordt geladen...';
    
    audio.onloadeddata = () => {
      audioStatus.textContent = '🎵 Muziek geladen - Start automatisch';
      setTimeout(() => {
        audio.play().catch(error => {
          audioStatus.textContent = '🎵 Klik op ▶️ om muziek te starten';
        });
      }, 500);
    };
    
    audio.onerror = () => {
      audioStatus.textContent = '❌ Kan muziek niet afspelen';
    };
    
    audio.src = song.path;
  }

  generateQuestion(song) {
    // For simplicity, we'll create artist questions
    const questionTypes = ['artist', 'title', 'year'];
    const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)];
    
    let questionText = '';
    let correctAnswer = '';
    
    switch (questionType) {
      case 'artist':
        questionText = 'Welke artiest zingt dit nummer?';
        correctAnswer = song.artist;
        break;
      case 'title':
        questionText = 'Wat is de titel van dit nummer?';
        correctAnswer = song.title;
        break;
      case 'year':
        questionText = 'In welk jaar werd dit nummer uitgebracht?';
        correctAnswer = song.year.toString();
        break;
    }
    
    document.getElementById('question-text').textContent = questionText;
    
    // Generate options
    this.generateOptions(questionType, correctAnswer, song);
  }

  generateOptions(questionType, correctAnswer, song) {
    const options = [correctAnswer];
    
    // Generate 3 wrong options based on question type
    while (options.length < 4) {
      let wrongOption = '';
      
      switch (questionType) {
        case 'artist':
          wrongOption = this.getRandomArtist();
          break;
        case 'title':
          wrongOption = this.getRandomTitle();
          break;
        case 'year':
          wrongOption = this.getRandomYear(song.year).toString();
          break;
      }
      
      if (!options.includes(wrongOption)) {
        options.push(wrongOption);
      }
    }
    
    // Shuffle options
    const shuffledOptions = options.sort(() => 0.5 - Math.random());
    
    // Set options in UI
    ['A', 'B', 'C', 'D'].forEach((letter, index) => {
      document.getElementById(`option-${letter}-text`).textContent = shuffledOptions[index];
    });
    
    // Store correct answer
    this.correctAnswer = correctAnswer;
    this.optionMapping = {};
    ['A', 'B', 'C', 'D'].forEach((letter, index) => {
      this.optionMapping[letter] = shuffledOptions[index];
    });
  }

  getRandomArtist() {
    const artists = ['Beyoncé', 'Justin Timberlake', 'Madonna', 'Michael Jackson', 'Rihanna', 'Drake', 'Taylor Swift', 'Ed Sheeran'];
    return artists[Math.floor(Math.random() * artists.length)];
  }

  getRandomTitle() {
    const titles = ['Love Song', 'Dancing Queen', 'Heartbreak Hotel', 'Summer Nights', 'Golden Dreams', 'City Lights', 'Midnight Blues'];
    return titles[Math.floor(Math.random() * titles.length)];
  }

  getRandomYear(correctYear) {
    const years = [];
    for (let i = correctYear - 3; i <= correctYear + 3; i++) {
      if (i !== correctYear && i >= 1960 && i <= 2025) {
        years.push(i);
      }
    }
    return years[Math.floor(Math.random() * years.length)];
  }

  submitAnswer(option) {
    if (this.hasAnswered) return;
    
    this.hasAnswered = true;
    const selectedAnswer = this.optionMapping[option];
    
    // Submit to multiplayer manager
    window.MultiplayerManager.submitAnswer(selectedAnswer);
    
    // Disable buttons and highlight selection
    this.disableAnswerButtons();
    document.getElementById(`answer-${option}`).classList.add('selected');
    
    // Update players status
    this.updatePlayersStatus();
    
    // Check if all players answered
    this.checkAllPlayersAnswered();
  }

  enableAnswerButtons() {
    ['A', 'B', 'C', 'D'].forEach(letter => {
      const btn = document.getElementById(`answer-${letter}`);
      btn.disabled = false;
      btn.classList.remove('selected', 'correct', 'incorrect');
    });
  }

  disableAnswerButtons() {
    ['A', 'B', 'C', 'D'].forEach(letter => {
      document.getElementById(`answer-${letter}`).disabled = true;
    });
  }

  updatePlayersStatus() {
    const room = window.MultiplayerManager.getRoomInfo();
    if (!room) return;
    
    const playersContainer = document.getElementById('players-answers');
    const players = Object.values(room.players);
    
    playersContainer.innerHTML = players.map(player => {
      const hasAnswered = player.answers[this.currentQuestion];
      const isCurrentPlayer = player.name === this.playerName;
      
      return `
        <div class="player-answer-status ${hasAnswered ? 'answered' : 'waiting'} ${isCurrentPlayer ? 'current-player' : ''}">
          <span class="player-name-status">${player.name}</span>
          <span class="answer-status-icon">${hasAnswered ? '✅' : '⏳'}</span>
        </div>
      `;
    }).join('');
  }

  checkAllPlayersAnswered() {
    if (window.MultiplayerManager.allPlayersAnswered()) {
      if (window.MultiplayerManager.isHost) {
        // Host moves to next question after a delay
        setTimeout(() => {
          this.showQuestionResults();
        }, 2000);
      } else {
        // Non-host waits for host
        this.showWaitingScreen();
      }
    }
  }

  showWaitingScreen() {
    document.getElementById('waiting-screen').classList.remove('hidden');
  }

  hideWaitingScreen() {
    document.getElementById('waiting-screen').classList.add('hidden');
  }

  showQuestionResults() {
    // Calculate and show results for current question
    const room = window.MultiplayerManager.getRoomInfo();
    if (!room) return;
    
    const resultsContainer = document.getElementById('question-results');
    const players = Object.values(room.players);
    
    resultsContainer.innerHTML = players.map(player => {
      const playerAnswer = player.answers[this.currentQuestion];
      const isCorrect = playerAnswer && playerAnswer.answer === this.correctAnswer;
      
      return `
        <div class="question-result-item ${isCorrect ? 'correct' : 'incorrect'}">
          <span class="result-player-name">${player.name}</span>
          <span class="result-answer">${playerAnswer ? playerAnswer.answer : 'Geen antwoord'}</span>
          <span class="result-points">${isCorrect ? '+10' : '+0'}</span>
        </div>
      `;
    }).join('');
    
    document.getElementById('results-screen').classList.remove('hidden');
    
    // Start countdown for next question
    this.startNextQuestionTimer();
  }

  startNextQuestionTimer() {
    let timeLeft = 5;
    document.getElementById('next-timer').textContent = timeLeft;
    
    this.nextQuestionTimer = setInterval(() => {
      timeLeft--;
      document.getElementById('next-timer').textContent = timeLeft;
      
      if (timeLeft <= 0) {
        clearInterval(this.nextQuestionTimer);
        this.hideResultsAndContinue();
      }
    }, 1000);
  }

  hideResultsAndContinue() {
    document.getElementById('results-screen').classList.add('hidden');
    this.hideWaitingScreen();
    
    if (window.MultiplayerManager.isHost) {
      window.MultiplayerManager.nextQuestion();
    }
    
    // Load next question
    const room = window.MultiplayerManager.getRoomInfo();
    this.currentQuestion = room.currentQuestion;
    this.loadCurrentQuestion();
  }

  showFinalResults() {
    const leaderboard = window.MultiplayerManager.getLeaderboard();
    const leaderboardContainer = document.getElementById('final-leaderboard');
    
    leaderboardContainer.innerHTML = leaderboard.map(player => {
      let placeClass = '';
      if (player.rank === 1) placeClass = 'first-place';
      else if (player.rank === 2) placeClass = 'second-place';
      else if (player.rank === 3) placeClass = 'third-place';
      
      return `
        <div class="final-leaderboard-item ${placeClass}">
          <div class="final-rank">${player.rank}</div>
          <div class="final-player-info">
            <div class="final-player-name">${player.name}</div>
            <div class="final-player-score">${player.score} punten</div>
          </div>
        </div>
      `;
    }).join('');
    
    document.getElementById('final-results-screen').classList.remove('hidden');
    this.stopPolling();
  }

  startPolling() {
    this.pollInterval = setInterval(() => {
      this.checkForUpdates();
    }, 2000);
  }

  stopPolling() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }
  }

  checkForUpdates() {
    const room = window.MultiplayerManager.getRoomInfo();
    if (!room) return;
    
    // Check if question changed
    if (room.currentQuestion !== this.currentQuestion) {
      this.currentQuestion = room.currentQuestion;
      this.hideWaitingScreen();
      document.getElementById('results-screen').classList.add('hidden');
      this.loadCurrentQuestion();
    }
    
    // Check if game finished
    if (room.gameState === 'finished') {
      this.showFinalResults();
    }
    
    // Update players status
    this.updatePlayersStatus();
  }
}

// Global functions
function submitAnswer(option) {
  if (window.multiplayerQuiz) {
    window.multiplayerQuiz.submitAnswer(option);
  }
}

function returnToLobby() {
  window.location.href = `../index.html`;
}

function goHome() {
  window.MultiplayerManager.leaveRoom();
  window.location.href = '../index.html';
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
  window.multiplayerQuiz = new MultiplayerQuiz();
});
