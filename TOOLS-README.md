# 🛠️ MorriQuiz Tools & Utilities

## 🚀 Server Management

### `start-server.bat` - Enhanced Server Launcher
**Verbeterde server starter met uitgebreide informatie en diagnostics**

**Features:**
- 🎨 **ASCII Art Logo** - Professionele uitstraling
- 📊 **System Checks** - Python en dependency validatie
- 🌐 **Network Detection** - Automatische IP detectie (Local + Tailscale)
- 📁 **Music Library Status** - Overzicht van beschikbare decades
- 🎮 **Feature Overview** - Singleplayer/Multiplayer info
- 💡 **Usage Tips** - Handige tips voor gebruikers

**Usage:**
```bash
# Windows
start-server.bat

# Of dubbelklik op het bestand
```

**Output Example:**
```
🎵 Muziek Quiz Server v2.0 - Multiplayer Edition
═══════════════════════════════════════════════════════════════════════

📊 System Check: Python ✅
🔗 Tailscale: Connected (*************)
🌐 Local Network: *************

🎮 Game Features:
├── 👤 Singleplayer: 100-question quiz per decade
├── 👥 Multiplayer: 20-question competitive quiz (2-4 players)
├── 🏆 Leaderboard with statistics and play time tracking
└── 🎵 Real-time audio streaming

📁 Music Library Status:
├── 60s: ✅ Available
├── 70s: ✅ Available
├── 80s: ✅ Available
└── ...

🌐 Access URLs:
═══════════════════════════════════════════════════════════════════════
📱 Local Access:     http://localhost:8080
🔗 Network Access:   http://*************:8080 (Tailscale)
```

---

## 🎵 Audio Processing Tools

### `convert-mp3-length.bat` - Windows MP3 Converter
**Batch script voor het optimaliseren van MP3 lengte voor quiz gebruik**

**Features:**
- ⏱️ **Optimal Length** - Converteert naar 30-45 seconden
- 🎯 **Smart Trimming** - Start op 25% van het nummer (meestal chorus)
- 💾 **Automatic Backup** - Originele bestanden bewaard als `.original`
- 📊 **Progress Tracking** - Real-time conversie status
- 🔄 **Batch Processing** - Alle decades automatisch

**Requirements:**
- **FFmpeg** - Download van https://ffmpeg.org/download.html
- **Windows** - Batch script voor Windows systemen

**Usage:**
```bash
# Windows
convert-mp3-length.bat

# Of dubbelklik op het bestand
```

### `convert-mp3-length.py` - Cross-Platform MP3 Converter
**Python script voor alle besturingssystemen**

**Features:**
- 🌍 **Cross-Platform** - Windows, macOS, Linux
- 🎵 **Duration Detection** - Automatische lengte detectie
- 🧠 **Smart Processing** - Slaat korte bestanden over
- 📈 **Detailed Reporting** - Uitgebreide conversie statistieken
- 🛡️ **Error Handling** - Robuuste foutafhandeling

**Requirements:**
- **Python 3.6+**
- **FFmpeg** - Geïnstalleerd en in PATH

**Installation:**
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt install ffmpeg

# Windows
# Download van https://ffmpeg.org/download.html
# Voeg toe aan PATH environment variable
```

**Usage:**
```bash
# Alle platforms
python convert-mp3-length.py

# Of direct uitvoeren (Unix/Linux/macOS)
./convert-mp3-length.py
```

---

## 📊 Conversion Process

### How It Works:
1. **🔍 Scan** - Detecteert alle MP3 bestanden in music/ directories
2. **⏱️ Analyze** - Bepaalt originele lengte van elk bestand
3. **💾 Backup** - Maakt backup van origineel bestand (.original)
4. **✂️ Trim** - Knipt bestand naar 35 seconden vanaf 25% positie
5. **🔄 Replace** - Vervangt origineel met getrimde versie
6. **📊 Report** - Toont conversie statistieken

### Conversion Settings:
```
🎯 Target Length: 35 seconds
📍 Start Position: 25% into song (catches chorus)
🎵 Audio Quality: Original bitrate maintained
📁 Backup Location: Same directory with .original extension
```

### Example Output:
```
📂 Processing decade: 80s
   🔄 Converting: Madonna - Like a Virgin - 1984.mp3
   ✅ Converted: Madonna - Like a Virgin - 1984.mp3 (245.3s → 35s)
   🔄 Converting: Duran Duran - Hungry Like the Wolf - 1984.mp3
   ✅ Converted: Duran Duran - Hungry Like the Wolf - 1984.mp3 (198.7s → 35s)

📊 80s Results: 100 processed, 98 converted, 2 errors
```

---

## 🔧 File Management Scripts

### `fix-all-filenames.py` - Filename Standardizer
**Converteert alle bestandsnamen naar standaard formaat**

**Format:** `Artist - Title - Year.mp3`

**Features:**
- 🧹 **Auto-Cleaning** - Verwijdert track nummers, remaster info
- 🎯 **Smart Parsing** - Intelligente artiest/titel extractie
- 📅 **Year Assignment** - Automatische jaar toewijzing per decade
- 🔄 **Batch Processing** - Alle decades in één keer

**Usage:**
```bash
python fix-all-filenames.py
```

---

## 🎮 Testing Tools

### `test-multiplayer.html` - Multiplayer Test Suite
**Uitgebreide test pagina voor multiplayer functionaliteit**

**Tests:**
- 🏠 **Room Creation** - Test room aanmaak
- 🚪 **Room Joining** - Test room join functionaliteit
- 📊 **Room Info** - Data retrieval validatie
- 🎵 **Music Loading** - Audio systeem test
- 💾 **Storage Test** - localStorage functionaliteit

**Access:** `http://localhost:8080/test-multiplayer.html`

---

## 📋 Quick Start Guide

### 1. Setup Server
```bash
# Start de server
start-server.bat

# Of voor andere platforms
python -m http.server 8080
```

### 2. Optimize Audio (Optional)
```bash
# Windows
convert-mp3-length.bat

# Andere platforms
python convert-mp3-length.py
```

### 3. Fix Filenames (If Needed)
```bash
python fix-all-filenames.py
```

### 4. Test Everything
```bash
# Open in browser
http://localhost:8080

# Test multiplayer
http://localhost:8080/test-multiplayer.html
```

---

## 🔍 Troubleshooting

### Common Issues:

**❌ FFmpeg not found**
- Download van https://ffmpeg.org/download.html
- Voeg toe aan PATH environment variable
- Herstart terminal/command prompt

**❌ Python not found**
- Install Python van https://python.org
- Zorg dat "Add to PATH" is aangevinkt tijdens installatie

**❌ Permission denied**
- Run als Administrator (Windows)
- Check file permissions (Unix/Linux)

**❌ Audio files not loading**
- Check bestandsnamen format: `Artist - Title - Year.mp3`
- Run `fix-all-filenames.py` om te repareren

---

## 📈 Performance Tips

### Audio Optimization:
- **Gebruik convert tools** voor snellere loading
- **35 seconden** is optimaal voor quiz gameplay
- **Backup bestanden** kunnen worden verwijderd na testing

### Server Performance:
- **Lokale toegang** is snelst (localhost:8080)
- **Tailscale** voor remote access
- **Firewall** configuratie voor netwerk toegang

---

**🎉 Alle tools zijn klaar voor gebruik!**

Voor vragen of problemen, check de console output voor gedetailleerde error messages.
