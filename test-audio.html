<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; }
        audio { width: 100%; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🎵 Audio Test</h1>
    
    <div class="test-section">
        <h3>Direct Audio Test</h3>
        <audio controls>
            <source src="music/10s/Vance Joy - Riptide - 2014.mp3" type="audio/mpeg">
            Je browser ondersteunt geen audio.
        </audio>
        <p>Direct link naar: <a href="music/10s/<PERSON> Joy - Riptide - 2014.mp3" target="_blank">Vance Joy - Riptide - 2014.mp3</a></p>
    </div>
    
    <div class="test-section">
        <h3>JavaScript Audio Test</h3>
        <button onclick="testAudio()">Test Audio Loading</button>
        <button onclick="playAudio()">Play Audio</button>
        <button onclick="pauseAudio()">Pause Audio</button>
        
        <div id="status"></div>
        
        <audio id="test-audio" controls style="width: 100%; margin-top: 10px;">
            Je browser ondersteunt geen audio.
        </audio>
    </div>
    
    <div class="test-section">
        <h3>Random File Test</h3>
        <button onclick="testRandomFile()">Test Random File</button>
        <div id="random-status"></div>
        <audio id="random-audio" controls style="width: 100%; margin-top: 10px;"></audio>
    </div>

    <script>
        let testAudioElement = null;
        
        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        function testAudio() {
            updateStatus('🔍 Testing audio loading...');
            
            testAudioElement = document.getElementById('test-audio');
            const testFile = 'music/10s/Vance Joy - Riptide - 2014.mp3';
            
            testAudioElement.onloadeddata = () => {
                updateStatus('✅ Audio loaded successfully!');
            };
            
            testAudioElement.oncanplay = () => {
                updateStatus('✅ Audio ready to play!');
            };
            
            testAudioElement.onerror = (error) => {
                updateStatus(`❌ Audio loading failed: ${error.type}`, true);
            };
            
            testAudioElement.src = testFile;
        }
        
        function playAudio() {
            if (!testAudioElement || !testAudioElement.src) {
                updateStatus('❌ No audio loaded. Click "Test Audio Loading" first.', true);
                return;
            }
            
            testAudioElement.play().then(() => {
                updateStatus('✅ Audio playing!');
            }).catch(error => {
                updateStatus(`❌ Play failed: ${error.message}`, true);
            });
        }
        
        function pauseAudio() {
            if (testAudioElement) {
                testAudioElement.pause();
                updateStatus('⏸️ Audio paused');
            }
        }
        
        async function testRandomFile() {
            const randomStatus = document.getElementById('random-status');
            const randomAudio = document.getElementById('random-audio');
            
            randomStatus.innerHTML = '<div class="status">🔍 Loading random file...</div>';
            
            try {
                // Get files from API
                const response = await fetch('/api/music/10s');
                const data = await response.json();
                
                if (data.files && data.files.length > 0) {
                    const randomFile = data.files[Math.floor(Math.random() * data.files.length)];
                    const filePath = `music/10s/${randomFile}`;
                    
                    randomStatus.innerHTML = `<div class="status">🎵 Testing: ${randomFile}</div>`;
                    
                    randomAudio.onloadeddata = () => {
                        randomStatus.innerHTML = `<div class="status success">✅ ${randomFile} loaded successfully!</div>`;
                    };
                    
                    randomAudio.onerror = () => {
                        randomStatus.innerHTML = `<div class="status error">❌ Failed to load ${randomFile}</div>`;
                    };
                    
                    randomAudio.src = filePath;
                } else {
                    randomStatus.innerHTML = '<div class="status error">❌ No files found in API</div>';
                }
            } catch (error) {
                randomStatus.innerHTML = `<div class="status error">❌ API Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
