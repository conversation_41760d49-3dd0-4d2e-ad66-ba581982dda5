<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Test</h1>
    <div id="output"></div>
    
    <script src="js/musicLoader.js"></script>
    <script>
        const output = document.getElementById('output');
        
        try {
            output.innerHTML += '<p>✅ musicLoader.js loaded successfully</p>';
            
            if (typeof MusicLoader !== 'undefined') {
                output.innerHTML += '<p>✅ MusicLoader class is available</p>';
                
                const loader = new MusicLoader();
                output.innerHTML += '<p>✅ MusicLoader instance created</p>';
                
                // Test API call
                loader.getMusicList('10s').then(files => {
                    output.innerHTML += `<p>✅ Found ${files.length} music files for 10s</p>`;
                    output.innerHTML += `<p>First file: ${files[0]}</p>`;
                }).catch(error => {
                    output.innerHTML += `<p>❌ Error loading music: ${error.message}</p>`;
                });
                
            } else {
                output.innerHTML += '<p>❌ MusicLoader class not found</p>';
            }
        } catch (error) {
            output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            console.error('JavaScript Error:', error);
        }
    </script>
</body>
</html>
